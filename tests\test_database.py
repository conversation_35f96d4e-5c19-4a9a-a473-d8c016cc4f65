"""
Unit tests for database management functionality
"""

import pytest
import sys
import os
import tempfile
import uuid
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.database import DatabaseManager
from src.exceptions import DatabaseError

class TestDatabaseManager:
    """Test cases for DatabaseManager functionality"""
    
    def setup_method(self):
        """Set up test database for each test"""
        # Use temporary database for testing
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DatabaseManager(self.temp_db.name)
    
    def teardown_method(self):
        """Clean up after each test"""
        # Close database connections first
        if hasattr(self, 'db'):
            del self.db

        # Remove temporary database
        try:
            if os.path.exists(self.temp_db.name):
                os.unlink(self.temp_db.name)
        except PermissionError:
            # On Windows, sometimes the file is still locked
            pass
    
    def test_database_initialization(self):
        """Test that database initializes correctly"""
        assert self.db is not None
        assert self.db.db_path.exists()
    
    def test_conversation_crud(self):
        """Test conversation CRUD operations"""
        conv_id = str(uuid.uuid4())
        title = "Test Conversation"
        
        # Create conversation
        result = self.db.create_conversation(conv_id, title)
        assert result is True
        
        # Get conversation
        conv = self.db.get_conversation(conv_id)
        assert conv is not None
        assert conv['id'] == conv_id
        assert conv['title'] == title
        
        # Update conversation
        new_title = "Updated Conversation"
        result = self.db.update_conversation(conv_id, title=new_title)
        assert result is True
        
        # Verify update
        conv = self.db.get_conversation(conv_id)
        assert conv['title'] == new_title
        
        # List conversations
        conversations = self.db.list_conversations()
        assert len(conversations) == 1
        assert conversations[0]['id'] == conv_id
        
        # Delete conversation
        result = self.db.delete_conversation(conv_id)
        assert result is True
        
        # Verify deletion
        conv = self.db.get_conversation(conv_id)
        assert conv is None
    
    def test_message_operations(self):
        """Test message CRUD operations"""
        # Create conversation first
        conv_id = str(uuid.uuid4())
        self.db.create_conversation(conv_id, "Test Conversation")
        
        # Add messages
        msg1_id = str(uuid.uuid4())
        msg2_id = str(uuid.uuid4())

        result1 = self.db.add_message(msg1_id, conv_id, "user", "Hello world")
        result2 = self.db.add_message(msg2_id, conv_id, "assistant", "Hi there!")

        assert result1 is True
        assert result2 is True

        # Get conversation messages
        messages = self.db.get_conversation_messages(conv_id)
        assert len(messages) == 2
        assert messages[0]['role'] == "user"
        assert messages[1]['role'] == "assistant"

        # Get recent messages (should return most recent first)
        recent = self.db.get_recent_messages(conv_id, 2)
        assert len(recent) == 2
        # Check that we have both messages (order may vary due to timestamp precision)
        roles = [msg['role'] for msg in recent]
        assert "user" in roles
        assert "assistant" in roles
        
        # Delete message
        result = self.db.delete_message(msg1_id)
        assert result is True
        
        # Verify deletion
        messages = self.db.get_conversation_messages(conv_id)
        assert len(messages) == 1
    
    def test_code_generation_tracking(self):
        """Test code generation tracking"""
        conv_id = str(uuid.uuid4())
        self.db.create_conversation(conv_id, "Test Conversation")
        
        gen_id = str(uuid.uuid4())
        result = self.db.save_code_generation(
            gen_id, conv_id, "python", "create hello world", 
            "print('Hello, World!')", "phi3:mini", True, []
        )
        assert result is True
        
        # Get code generations
        generations = self.db.get_code_generations(conversation_id=conv_id)
        assert len(generations) == 1
        assert generations[0]['language'] == "python"
        # SQLite returns 1/0 for boolean values
        assert generations[0]['is_valid'] == 1
    
    def test_project_tracking(self):
        """Test project metadata tracking"""
        conv_id = str(uuid.uuid4())
        self.db.create_conversation(conv_id, "Test Conversation")
        
        project_id = str(uuid.uuid4())
        result = self.db.save_project(
            project_id, "Test Project", "A test project", 
            "/path/to/project", conv_id, ["flask", "sqlite3"]
        )
        assert result is True
        
        # Get projects
        projects = self.db.get_projects(conversation_id=conv_id)
        assert len(projects) == 1
        assert projects[0]['name'] == "Test Project"
    
    def test_performance_metrics(self):
        """Test performance metrics logging"""
        metric_id = str(uuid.uuid4())
        result = self.db.log_performance_metric(
            metric_id, "code_generation", 1500, "phi3:mini", 150
        )
        assert result is True
        
        # Get performance stats
        stats = self.db.get_performance_stats()
        assert stats['count'] == 1
        assert stats['avg_duration'] == 1500.0
        assert stats['total_tokens'] == 150
    
    def test_database_error_handling(self):
        """Test database error handling"""
        # Test with invalid conversation ID
        result = self.db.update_conversation("invalid-id", title="Test")
        assert result is False
        
        # Test getting non-existent conversation
        conv = self.db.get_conversation("invalid-id")
        assert conv is None
