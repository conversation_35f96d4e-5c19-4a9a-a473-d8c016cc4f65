# AI Coding Agent - Database Management
"""
Database management for conversation history, code generations, and project metadata
"""

import sqlite3
from pathlib import Path
from typing import Optional

from .logger import get_logger
from .exceptions import DatabaseError
from .config import get_config

config = get_config()

class DatabaseManager:
    """
    Manages SQLite database for storing conversation history,
    code generations, and project metadata.
    """

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize database manager

        Args:
            db_path: Path to SQLite database file (defaults to config)
        """
        if db_path is None:
            # Extract path from DATABASE_URL config
            db_url = config.DATABASE_URL
            if db_url.startswith('sqlite:///'):
                db_path = db_url[10:]  # Remove 'sqlite:///' prefix
            else:
                db_path = "database/coding_agent.db"

        # Ensure db_path is not None
        if db_path is None:
            db_path = "database/coding_agent.db"

        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        self.logger = get_logger(__name__)

        # Initialize database schema
        self.init_database()

    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with proper configuration"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign key constraints
            return conn
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise DatabaseError(f"Database connection failed: {e}")

    def init_database(self):
        """Initialize database with required tables"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Conversations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL DEFAULT 'New Conversation',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}',
                        max_context_length INTEGER DEFAULT 4000
                    )
                ''')

                # Messages table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id TEXT PRIMARY KEY,
                        conversation_id TEXT NOT NULL,
                        role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
                        content TEXT NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}',
                        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
                    )
                ''')

                # Code generations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS code_generations (
                        id TEXT PRIMARY KEY,
                        conversation_id TEXT,
                        language TEXT NOT NULL,
                        prompt TEXT NOT NULL,
                        code TEXT NOT NULL,
                        model_used TEXT,
                        is_valid BOOLEAN DEFAULT 0,
                        validation_errors TEXT DEFAULT '[]',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}',
                        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE SET NULL
                    )
                ''')

                # Projects table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS projects (
                        id TEXT PRIMARY KEY,
                        name TEXT UNIQUE NOT NULL,
                        description TEXT,
                        file_path TEXT,
                        conversation_id TEXT,
                        dependencies TEXT DEFAULT '[]',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}',
                        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE SET NULL
                    )
                ''')

                # Performance metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id TEXT PRIMARY KEY,
                        operation TEXT NOT NULL,
                        duration_ms INTEGER NOT NULL,
                        model_used TEXT,
                        tokens_used INTEGER DEFAULT 0,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}'
                    )
                ''')

                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_code_generations_conversation_id ON code_generations (conversation_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_projects_conversation_id ON projects (conversation_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics (timestamp)')

                conn.commit()
                self.logger.info("Database schema initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise DatabaseError(f"Database initialization failed: {e}")

    # Conversation CRUD operations
    def create_conversation(self, conversation_id: str, title: str = "New Conversation",
                          metadata: Optional[dict] = None) -> bool:
        """Create a new conversation"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO conversations (id, title, metadata)
                    VALUES (?, ?, ?)
                ''', (conversation_id, title, str(metadata or {})))
                conn.commit()
                self.logger.info(f"Created conversation: {conversation_id} - {title}")
                return True
        except Exception as e:
            self.logger.error(f"Failed to create conversation: {e}")
            raise DatabaseError(f"Failed to create conversation: {e}")

    def get_conversation(self, conversation_id: str) -> Optional[dict]:
        """Get conversation by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title, created_at, updated_at, metadata, max_context_length
                    FROM conversations WHERE id = ?
                ''', (conversation_id,))
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
        except Exception as e:
            self.logger.error(f"Failed to get conversation: {e}")
            raise DatabaseError(f"Failed to get conversation: {e}")

    def update_conversation(self, conversation_id: str, title: Optional[str] = None,
                          metadata: Optional[dict] = None) -> bool:
        """Update conversation details"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                updates = []
                params = []

                if title is not None:
                    updates.append("title = ?")
                    params.append(title)

                if metadata is not None:
                    updates.append("metadata = ?")
                    params.append(str(metadata))

                if updates:
                    updates.append("updated_at = CURRENT_TIMESTAMP")
                    params.append(conversation_id)

                    query = f"UPDATE conversations SET {', '.join(updates)} WHERE id = ?"
                    cursor.execute(query, params)
                    conn.commit()
                    return cursor.rowcount > 0
                return False
        except Exception as e:
            self.logger.error(f"Failed to update conversation: {e}")
            raise DatabaseError(f"Failed to update conversation: {e}")

    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete conversation and all associated messages"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM conversations WHERE id = ?', (conversation_id,))
                conn.commit()
                deleted = cursor.rowcount > 0
                if deleted:
                    self.logger.info(f"Deleted conversation: {conversation_id}")
                return deleted
        except Exception as e:
            self.logger.error(f"Failed to delete conversation: {e}")
            raise DatabaseError(f"Failed to delete conversation: {e}")

    def list_conversations(self, limit: int = 50, offset: int = 0) -> list:
        """List all conversations with basic info"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT c.id, c.title, c.created_at, c.updated_at,
                           COUNT(m.id) as message_count
                    FROM conversations c
                    LEFT JOIN messages m ON c.id = m.conversation_id
                    GROUP BY c.id, c.title, c.created_at, c.updated_at
                    ORDER BY c.updated_at DESC
                    LIMIT ? OFFSET ?
                ''', (limit, offset))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Failed to list conversations: {e}")
            raise DatabaseError(f"Failed to list conversations: {e}")

    # Message CRUD operations
    def add_message(self, message_id: str, conversation_id: str, role: str,
                   content: str, metadata: Optional[dict] = None) -> bool:
        """Add a message to a conversation"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO messages (id, conversation_id, role, content, metadata)
                    VALUES (?, ?, ?, ?, ?)
                ''', (message_id, conversation_id, role, content, str(metadata or {})))

                # Update conversation's updated_at timestamp
                cursor.execute('''
                    UPDATE conversations SET updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (conversation_id,))

                conn.commit()
                self.logger.debug(f"Added {role} message to conversation {conversation_id}")
                return True
        except Exception as e:
            self.logger.error(f"Failed to add message: {e}")
            raise DatabaseError(f"Failed to add message: {e}")

    def get_conversation_messages(self, conversation_id: str, limit: int = 100,
                                offset: int = 0) -> list:
        """Get messages for a conversation"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, role, content, timestamp, metadata
                    FROM messages
                    WHERE conversation_id = ?
                    ORDER BY timestamp ASC
                    LIMIT ? OFFSET ?
                ''', (conversation_id, limit, offset))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Failed to get conversation messages: {e}")
            raise DatabaseError(f"Failed to get conversation messages: {e}")

    def get_recent_messages(self, conversation_id: str, count: int = 10) -> list:
        """Get recent messages for context"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, role, content, timestamp, metadata
                    FROM messages
                    WHERE conversation_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ''', (conversation_id, count))
                # Return most recent first (DESC order)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Failed to get recent messages: {e}")
            raise DatabaseError(f"Failed to get recent messages: {e}")

    def delete_message(self, message_id: str) -> bool:
        """Delete a specific message"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM messages WHERE id = ?', (message_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            self.logger.error(f"Failed to delete message: {e}")
            raise DatabaseError(f"Failed to delete message: {e}")

    # Code generation tracking
    def save_code_generation(self, generation_id: str, conversation_id: Optional[str],
                           language: str, prompt: str, code: str, model_used: str,
                           is_valid: bool = False, validation_errors: Optional[list] = None,
                           metadata: Optional[dict] = None) -> bool:
        """Save a code generation result"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO code_generations
                    (id, conversation_id, language, prompt, code, model_used,
                     is_valid, validation_errors, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (generation_id, conversation_id, language, prompt, code,
                      model_used, is_valid, str(validation_errors or []),
                      str(metadata or {})))
                conn.commit()
                self.logger.info(f"Saved code generation: {language} - {len(code)} chars")
                return True
        except Exception as e:
            self.logger.error(f"Failed to save code generation: {e}")
            raise DatabaseError(f"Failed to save code generation: {e}")

    def get_code_generations(self, conversation_id: Optional[str] = None,
                           language: Optional[str] = None, limit: int = 50) -> list:
        """Get code generations with optional filters"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT id, conversation_id, language, prompt, code, model_used,
                           is_valid, validation_errors, created_at, metadata
                    FROM code_generations
                '''
                params = []
                conditions = []

                if conversation_id:
                    conditions.append("conversation_id = ?")
                    params.append(conversation_id)

                if language:
                    conditions.append("language = ?")
                    params.append(language)

                if conditions:
                    query += " WHERE " + " AND ".join(conditions)

                query += " ORDER BY created_at DESC LIMIT ?"
                params.append(limit)

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Failed to get code generations: {e}")
            raise DatabaseError(f"Failed to get code generations: {e}")

    # Project metadata tracking
    def save_project(self, project_id: str, name: str, description: Optional[str] = None,
                    file_path: Optional[str] = None, conversation_id: Optional[str] = None,
                    dependencies: Optional[list] = None, metadata: Optional[dict] = None) -> bool:
        """Save project metadata"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO projects
                    (id, name, description, file_path, conversation_id, dependencies, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (project_id, name, description, file_path, conversation_id,
                      str(dependencies or []), str(metadata or {})))
                conn.commit()
                self.logger.info(f"Saved project: {name}")
                return True
        except Exception as e:
            self.logger.error(f"Failed to save project: {e}")
            raise DatabaseError(f"Failed to save project: {e}")

    def get_projects(self, conversation_id: Optional[str] = None, limit: int = 50) -> list:
        """Get projects with optional conversation filter"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if conversation_id:
                    cursor.execute('''
                        SELECT id, name, description, file_path, conversation_id,
                               dependencies, created_at, updated_at, metadata
                        FROM projects
                        WHERE conversation_id = ?
                        ORDER BY updated_at DESC LIMIT ?
                    ''', (conversation_id, limit))
                else:
                    cursor.execute('''
                        SELECT id, name, description, file_path, conversation_id,
                               dependencies, created_at, updated_at, metadata
                        FROM projects
                        ORDER BY updated_at DESC LIMIT ?
                    ''', (limit,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Failed to get projects: {e}")
            raise DatabaseError(f"Failed to get projects: {e}")

    # Performance metrics
    def log_performance_metric(self, metric_id: str, operation: str, duration_ms: int,
                             model_used: Optional[str] = None, tokens_used: int = 0,
                             metadata: Optional[dict] = None) -> bool:
        """Log a performance metric"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO performance_metrics
                    (id, operation, duration_ms, model_used, tokens_used, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (metric_id, operation, duration_ms, model_used, tokens_used,
                      str(metadata or {})))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Failed to log performance metric: {e}")
            return False

    def get_performance_stats(self, operation: Optional[str] = None,
                            hours: int = 24) -> dict:
        """Get performance statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                base_query = '''
                    SELECT COUNT(*) as count, AVG(duration_ms) as avg_duration,
                           MIN(duration_ms) as min_duration, MAX(duration_ms) as max_duration,
                           SUM(tokens_used) as total_tokens
                    FROM performance_metrics
                    WHERE timestamp > datetime('now', '-{} hours')
                '''.format(hours)

                if operation:
                    base_query += " AND operation = ?"
                    cursor.execute(base_query, (operation,))
                else:
                    cursor.execute(base_query)

                row = cursor.fetchone()
                return dict(row) if row else {}
        except Exception as e:
            self.logger.error(f"Failed to get performance stats: {e}")
            return {}
