["tests/test_agent.py::TestCodingAgent::test_agent_initialization", "tests/test_agent.py::TestCodingAgent::test_generate_code_basic", "tests/test_agent.py::TestCodingAgent::test_session_id_uniqueness", "tests/test_conversation.py::TestConversation::test_add_message", "tests/test_conversation.py::TestConversation::test_auto_title_generation", "tests/test_conversation.py::TestConversation::test_conversation_creation", "tests/test_conversation.py::TestConversation::test_conversation_serialization", "tests/test_conversation.py::TestConversation::test_get_context_messages", "tests/test_conversation.py::TestConversation::test_get_formatted_context", "tests/test_conversation.py::TestConversationManager::test_add_message_to_current", "tests/test_conversation.py::TestConversationManager::test_conversation_list", "tests/test_conversation.py::TestConversationManager::test_conversation_management", "tests/test_conversation.py::TestConversationManager::test_create_conversation", "tests/test_conversation.py::TestConversationManager::test_delete_conversation", "tests/test_conversation.py::TestConversationManager::test_manager_creation", "tests/test_conversation.py::TestMessage::test_message_creation", "tests/test_conversation.py::TestMessage::test_message_from_dict", "tests/test_conversation.py::TestMessage::test_message_to_dict", "tests/test_database.py::TestDatabaseManager::test_code_generation_tracking", "tests/test_database.py::TestDatabaseManager::test_conversation_crud", "tests/test_database.py::TestDatabaseManager::test_database_error_handling", "tests/test_database.py::TestDatabaseManager::test_database_initialization", "tests/test_database.py::TestDatabaseManager::test_message_operations", "tests/test_database.py::TestDatabaseManager::test_performance_metrics", "tests/test_database.py::TestDatabaseManager::test_project_tracking", "tests/test_integration.py::TestIntegration::test_agent_initialization_integration", "tests/test_integration.py::TestIntegration::test_code_generation_with_validation_integration", "tests/test_integration.py::TestIntegration::test_component_isolation", "tests/test_integration.py::TestIntegration::test_conversation_persistence_integration", "tests/test_integration.py::TestIntegration::test_database_integration", "tests/test_integration.py::TestIntegration::test_error_handling_integration", "tests/test_integration.py::TestIntegration::test_llm_conversation_context_integration", "tests/test_integration.py::TestIntegration::test_validator_integration", "tests/test_integration.py::TestPerformanceIntegration::test_code_generation_performance", "tests/test_integration.py::TestPerformanceIntegration::test_validation_performance", "tests/test_integration.py::TestSecurityIntegration::test_dangerous_code_detection", "tests/test_integration.py::TestSecurityIntegration::test_safe_code_acceptance", "tests/test_llm_integration.py::TestLLMManager::test_error_handling", "tests/test_llm_integration.py::TestLLMManager::test_generate_response_basic", "tests/test_llm_integration.py::TestLLMManager::test_get_model_config", "tests/test_llm_integration.py::TestLLMManager::test_llm_manager_initialization", "tests/test_llm_integration.py::TestLLMManager::test_model_configurations", "tests/test_llm_integration.py::TestLLMManager::test_ollama_connection", "tests/test_llm_integration.py::TestModelConfig::test_model_config_creation", "tests/test_logging.py::TestExceptions::test_base_exception", "tests/test_logging.py::TestExceptions::test_exception_inheritance", "tests/test_logging.py::TestExceptions::test_llm_connection_error", "tests/test_logging.py::TestLogging::test_config_loading", "tests/test_logging.py::TestLogging::test_logger_creation", "tests/test_logging.py::TestLogging::test_logger_instance", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_closed_state", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_decorator", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_opens_after_failures", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_recovery", "tests/test_monitoring.py::TestMonitoringIntegration::test_database_health_check", "tests/test_monitoring.py::TestMonitoringIntegration::test_global_performance_monitor", "tests/test_monitoring.py::TestMonitoringIntegration::test_ollama_health_check", "tests/test_monitoring.py::TestMonitoringIntegration::test_performance_monitor_database_integration", "tests/test_monitoring.py::TestPerformanceMonitor::test_measure_operation_context_manager", "tests/test_monitoring.py::TestPerformanceMonitor::test_measure_operation_with_exception", "tests/test_monitoring.py::TestPerformanceMonitor::test_performance_monitor_initialization", "tests/test_monitoring.py::TestPerformanceMonitor::test_performance_summary", "tests/test_monitoring.py::TestPerformanceMonitor::test_system_health_check", "tests/test_monitoring.py::TestPerformanceMonitor::test_system_health_warning_status", "tests/test_monitoring.py::TestPerformanceThresholds::test_threshold_warning", "tests/test_security.py::TestCodeSandbox::test_attribute_access_detection", "tests/test_security.py::TestCodeSandbox::test_dangerous_code_detection", "tests/test_security.py::TestCodeSandbox::test_safe_code_detection", "tests/test_security.py::TestInputSanitizer::test_dangerous_pattern_detection", "tests/test_security.py::TestInputSanitizer::test_filename_sanitization", "tests/test_security.py::TestInputSanitizer::test_html_escape_sanitization", "tests/test_security.py::TestInputSanitizer::test_json_validation", "tests/test_security.py::TestInputSanitizer::test_length_validation", "tests/test_security.py::TestInputSanitizer::test_safe_text_sanitization", "tests/test_security.py::TestRateLimiter::test_multiple_clients", "tests/test_security.py::TestRateLimiter::test_rate_limiting", "tests/test_security.py::TestRateLimiter::test_remaining_requests", "tests/test_security.py::TestSecurityAuditor::test_security_event_logging", "tests/test_security.py::TestSecurityIntegration::test_dangerous_input_workflow", "tests/test_security.py::TestSecurityIntegration::test_security_components_integration"]