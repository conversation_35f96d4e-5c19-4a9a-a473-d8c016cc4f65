"""
Security utilities and input sanitization for the AI Coding Agent
"""

import re
import html
import json
import hashlib
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from .logger import get_logger
from .exceptions import SecurityError

class InputSanitizer:
    """
    Input sanitization and validation utilities
    """
    
    def __init__(self):
        """Initialize input sanitizer"""
        self.logger = get_logger(__name__)
        
        # Dangerous patterns for code injection (balanced approach)
        self.dangerous_patterns = [
            # Command injection patterns
            r'(rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/',  # Dangerous commands with paths
            r'`[^`]*`',  # Backtick command execution
            r'\$\([^)]*\)',  # Command substitution

            # SQL injection patterns
            r';\s*(drop|delete)\s+(table|database|from)',
            r'union\s+select\s+',  # Union select attacks
            r'--\s*[^\r\n]*',  # SQL comments (but allow in code contexts)
            r'/\*.*?\*/',  # SQL block comments

            # Script injection
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',  # Event handlers

            # Path traversal (system file access)
            r'\.\./+(etc|windows|system32)',
            r'\.\.\\+(etc|windows|system32)',

            # Dangerous function calls
            r'(eval|exec)\s*\(',  # Code execution functions
            r'(system|popen|subprocess)\s*[\.\(]',  # System command functions (including subprocess.call)
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE | re.DOTALL) 
                                for pattern in self.dangerous_patterns]
    
    def sanitize_text_input(self, text: str, max_length: int = 10000,
                           escape_html: bool = False) -> str:
        """
        Sanitize text input for safe processing

        Args:
            text: Input text to sanitize
            max_length: Maximum allowed length
            escape_html: Whether to HTML escape the text (for web contexts)

        Returns:
            Sanitized text

        Raises:
            SecurityError: If input contains dangerous patterns
        """
        if not isinstance(text, str):
            raise SecurityError("Input must be a string")

        # Check length
        if len(text) > max_length:
            raise SecurityError(f"Input too long: {len(text)} > {max_length}")

        # Check for dangerous patterns
        for pattern in self.compiled_patterns:
            if pattern.search(text):
                self.logger.warning(f"Dangerous pattern detected: {pattern.pattern}")
                raise SecurityError(f"Input contains potentially dangerous content")

        # HTML escape only if requested (for web contexts)
        sanitized = html.escape(text) if escape_html else text

        # Remove null bytes and control characters
        sanitized = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', sanitized)

        return sanitized
    
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for safe file operations
        
        Args:
            filename: Filename to sanitize
            
        Returns:
            Safe filename
        """
        if not isinstance(filename, str):
            raise SecurityError("Filename must be a string")
        
        # Remove path separators and dangerous characters (replace each char with _)
        sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)

        # Handle path traversal patterns: replace .. with _..
        sanitized = re.sub(r'\.\.', '_..', sanitized)
        
        # Remove leading/trailing dots and spaces
        sanitized = sanitized.strip('. ')
        
        # Ensure not empty
        if not sanitized:
            sanitized = "untitled"
        
        # Limit length
        if len(sanitized) > 255:
            sanitized = sanitized[:255]
        
        return sanitized
    
    def validate_json_input(self, json_str: str, max_depth: int = 10) -> Dict[str, Any]:
        """
        Safely parse and validate JSON input
        
        Args:
            json_str: JSON string to parse
            max_depth: Maximum nesting depth allowed
            
        Returns:
            Parsed JSON object
            
        Raises:
            SecurityError: If JSON is invalid or unsafe
        """
        try:
            # Parse JSON
            data = json.loads(json_str)
            
            # Check depth
            if self._get_json_depth(data) > max_depth:
                raise SecurityError(f"JSON nesting too deep (max: {max_depth})")
            
            # Check for dangerous keys
            dangerous_keys = ['__proto__', 'constructor', 'prototype']
            if self._contains_dangerous_keys(data, dangerous_keys):
                raise SecurityError("JSON contains dangerous keys")
            
            return data
            
        except json.JSONDecodeError as e:
            raise SecurityError(f"Invalid JSON: {e}")
    
    def _get_json_depth(self, obj: Any, depth: int = 0) -> int:
        """Calculate maximum depth of JSON object"""
        if isinstance(obj, dict):
            return max([self._get_json_depth(v, depth + 1) for v in obj.values()], default=depth)
        elif isinstance(obj, list):
            return max([self._get_json_depth(item, depth + 1) for item in obj], default=depth)
        else:
            return depth
    
    def _contains_dangerous_keys(self, obj: Any, dangerous_keys: List[str]) -> bool:
        """Check if object contains dangerous keys"""
        if isinstance(obj, dict):
            for key in obj.keys():
                if key in dangerous_keys:
                    return True
                if self._contains_dangerous_keys(obj[key], dangerous_keys):
                    return True
        elif isinstance(obj, list):
            for item in obj:
                if self._contains_dangerous_keys(item, dangerous_keys):
                    return True
        return False

class CodeSandbox:
    """
    Secure code execution sandbox (placeholder for future implementation)
    """
    
    def __init__(self):
        """Initialize code sandbox"""
        self.logger = get_logger(__name__)
        self.allowed_modules = {
            'math', 'random', 'datetime', 'json', 're', 'string', 'collections',
            'itertools', 'functools', 'operator', 'typing'
        }
        
        self.blocked_modules = {
            'os', 'sys', 'subprocess', 'socket', 'urllib', 'requests',
            'shutil', 'glob', 'tempfile', 'pickle', 'marshal', 'imp',
            'importlib', '__builtin__', 'builtins'
        }
    
    def is_safe_to_execute(self, code: str) -> tuple[bool, List[str]]:
        """
        Check if code is safe to execute (basic static analysis)
        
        Args:
            code: Python code to check
            
        Returns:
            Tuple of (is_safe, list_of_issues)
        """
        issues = []
        
        # Check for blocked modules
        for module in self.blocked_modules:
            if re.search(rf'\b(import\s+{module}|from\s+{module})', code):
                issues.append(f"Blocked module import: {module}")
        
        # Check for dangerous functions
        dangerous_functions = ['eval', 'exec', 'compile', '__import__', 'open', 'file']
        for func in dangerous_functions:
            if re.search(rf'\b{func}\s*\(', code):
                issues.append(f"Dangerous function call: {func}")
        
        # Check for attribute access that could be dangerous
        dangerous_attrs = ['__globals__', '__locals__', '__dict__', '__class__', '__bases__']
        for attr in dangerous_attrs:
            if attr in code:
                issues.append(f"Dangerous attribute access: {attr}")
        
        is_safe = len(issues) == 0
        return is_safe, issues

class RateLimiter:
    """
    Simple rate limiting for API endpoints
    """
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 3600):
        """
        Initialize rate limiter
        
        Args:
            max_requests: Maximum requests per window
            window_seconds: Time window in seconds
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # client_id -> list of timestamps
        self.logger = get_logger(__name__)
    
    def is_allowed(self, client_id: str) -> bool:
        """
        Check if client is allowed to make a request
        
        Args:
            client_id: Unique client identifier
            
        Returns:
            True if request is allowed, False otherwise
        """
        import time
        
        current_time = time.time()
        
        # Initialize client if not exists
        if client_id not in self.requests:
            self.requests[client_id] = []
        
        # Remove old requests outside the window
        self.requests[client_id] = [
            req_time for req_time in self.requests[client_id]
            if current_time - req_time < self.window_seconds
        ]
        
        # Check if under limit
        if len(self.requests[client_id]) < self.max_requests:
            self.requests[client_id].append(current_time)
            return True
        else:
            self.logger.warning(f"Rate limit exceeded for client: {client_id}")
            return False
    
    def get_remaining_requests(self, client_id: str) -> int:
        """Get remaining requests for client"""
        if client_id not in self.requests:
            return self.max_requests
        
        import time
        current_time = time.time()
        
        # Count recent requests
        recent_requests = [
            req_time for req_time in self.requests[client_id]
            if current_time - req_time < self.window_seconds
        ]
        
        return max(0, self.max_requests - len(recent_requests))

class SecurityAuditor:
    """
    Security auditing and logging utilities
    """
    
    def __init__(self):
        """Initialize security auditor"""
        self.logger = get_logger(__name__)
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          severity: str = "warning"):
        """
        Log security-related events
        
        Args:
            event_type: Type of security event
            details: Event details
            severity: Event severity (info, warning, error, critical)
        """
        log_entry = {
            "event_type": event_type,
            "severity": severity,
            "details": details,
            "timestamp": self._get_timestamp()
        }
        
        if severity == "critical":
            self.logger.critical(f"SECURITY: {event_type} - {details}")
        elif severity == "error":
            self.logger.error(f"SECURITY: {event_type} - {details}")
        elif severity == "warning":
            self.logger.warning(f"SECURITY: {event_type} - {details}")
        else:
            self.logger.info(f"SECURITY: {event_type} - {details}")
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()

# Global instances
_input_sanitizer = None
_code_sandbox = None
_rate_limiter = None
_security_auditor = None

def get_input_sanitizer() -> InputSanitizer:
    """Get global input sanitizer instance"""
    global _input_sanitizer
    if _input_sanitizer is None:
        _input_sanitizer = InputSanitizer()
    return _input_sanitizer

def get_code_sandbox() -> CodeSandbox:
    """Get global code sandbox instance"""
    global _code_sandbox
    if _code_sandbox is None:
        _code_sandbox = CodeSandbox()
    return _code_sandbox

def get_rate_limiter() -> RateLimiter:
    """Get global rate limiter instance"""
    global _rate_limiter
    if _rate_limiter is None:
        _rate_limiter = RateLimiter()
    return _rate_limiter

def get_security_auditor() -> SecurityAuditor:
    """Get global security auditor instance"""
    global _security_auditor
    if _security_auditor is None:
        _security_auditor = SecurityAuditor()
    return _security_auditor
