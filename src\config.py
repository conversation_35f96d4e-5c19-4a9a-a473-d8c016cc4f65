# AI Coding Agent - Configuration Management
"""
Configuration management for the coding agent
"""

import os
import sys
import platform
import logging
from pathlib import Path
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class"""
    
    # Project paths
    BASE_DIR = Path(__file__).parent.parent
    SRC_DIR = BASE_DIR / "src"
    DATABASE_DIR = BASE_DIR / "database"
    LOGS_DIR = BASE_DIR / "logs"
    PROJECTS_DIR = BASE_DIR / "projects"
    TEMPLATES_DIR = BASE_DIR / "templates"
    STATIC_DIR = BASE_DIR / "static"
    
    # Ensure directories exist
    DATABASE_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)
    PROJECTS_DIR.mkdir(exist_ok=True)

    # Environment validation
    @classmethod
    def validate_environment(cls) -> Dict[str, Any]:
        """
        Validate the environment and return system information

        Returns:
            Dictionary with environment validation results
        """
        validation_results = {
            "status": "valid",
            "issues": [],
            "system_info": {
                "python_version": platform.python_version(),
                "os_platform": platform.platform(),
                "processor": platform.processor(),
                "memory": cls._get_system_memory(),
            },
            "dependencies": cls._check_dependencies(),
            "directories": cls._check_directories(),
            "environment_variables": cls._check_environment_variables()
        }

        # Check if there are any issues
        if validation_results["issues"]:
            validation_results["status"] = "invalid"

        return validation_results

    @classmethod
    def _get_system_memory(cls) -> str:
        """Get system memory information"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return f"{memory.total / (1024**3):.1f} GB (Available: {memory.available / (1024**3):.1f} GB)"
        except ImportError:
            return "Unknown (psutil not installed)"

    @classmethod
    def _check_dependencies(cls) -> Dict[str, bool]:
        """Check if required dependencies are installed"""
        dependencies = {
            "ollama": cls._check_command_exists("ollama"),
            "python": True,  # We're running Python
            "pip": cls._check_command_exists("pip"),
            "git": cls._check_command_exists("git"),
            "node": cls._check_command_exists("node"),
            "npm": cls._check_command_exists("npm")
        }

        # Check Python packages
        try:
            import pkg_resources
            required_packages = ["pytest", "requests", "rich", "pydantic"]
            for package in required_packages:
                try:
                    pkg_resources.get_distribution(package)
                    dependencies[package] = True
                except pkg_resources.DistributionNotFound:
                    dependencies[package] = False
        except ImportError:
            pass

        return dependencies

    @classmethod
    def _check_command_exists(cls, command: str) -> bool:
        """Check if a command exists in the system path"""
        import shutil
        return shutil.which(command) is not None

    @classmethod
    def _check_directories(cls) -> Dict[str, bool]:
        """Check if required directories exist and are writable"""
        directories = {
            "base_dir": os.path.exists(cls.BASE_DIR) and os.access(cls.BASE_DIR, os.W_OK),
            "src_dir": os.path.exists(cls.SRC_DIR) and os.access(cls.SRC_DIR, os.R_OK),
            "database_dir": os.path.exists(cls.DATABASE_DIR) and os.access(cls.DATABASE_DIR, os.W_OK),
            "logs_dir": os.path.exists(cls.LOGS_DIR) and os.access(cls.LOGS_DIR, os.W_OK),
            "projects_dir": os.path.exists(cls.PROJECTS_DIR) and os.access(cls.PROJECTS_DIR, os.W_OK),
            "templates_dir": os.path.exists(cls.TEMPLATES_DIR) if hasattr(cls, "TEMPLATES_DIR") else False,
            "static_dir": os.path.exists(cls.STATIC_DIR) if hasattr(cls, "STATIC_DIR") else False
        }
        return directories

    @classmethod
    def _check_environment_variables(cls) -> Dict[str, bool]:
        """Check if required environment variables are set"""
        required_vars = [
            "OLLAMA_BASE_URL",
            "DATABASE_URL",
            "LOG_LEVEL"
        ]

        env_vars = {}
        for var in required_vars:
            env_vars[var] = var in os.environ

        return env_vars
    
    # Environment
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
    DEBUG = os.getenv('DEBUG', 'true').lower() == 'true'
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Ollama Configuration
    OLLAMA_HOST = os.getenv('OLLAMA_HOST', 'http://localhost:11434')
    OLLAMA_TIMEOUT = int(os.getenv('OLLAMA_TIMEOUT', '30'))
    
    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL', f'sqlite:///{DATABASE_DIR}/coding_agent.db')
    
    # API Configuration
    API_HOST = os.getenv('API_HOST', 'localhost')
    API_PORT = int(os.getenv('API_PORT', '5000'))
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = LOGS_DIR / os.getenv('LOG_FILE', 'coding_agent.log')
    LOG_MAX_BYTES = int(os.getenv('LOG_MAX_BYTES', '10485760'))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', '5'))
    
    # Security
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:3000,http://localhost:5000').split(',')

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    DATABASE_URL = 'sqlite:///:memory:'

# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

def get_config():
    """Get configuration based on environment"""
    env = os.getenv('ENVIRONMENT', 'development')
    return config_map.get(env, DevelopmentConfig)
