# AI Coding Agent - Main Agent Implementation
"""
Main CodingAgent class that orchestrates all components
"""

import uuid

from .logger import get_logger
from .exceptions import CodingAgentError
from .config import get_config
from .models import LLMManager
from .conversation import ConversationManager
from .validators import CodeValidator
from .database import DatabaseManager
from .monitoring import get_performance_monitor, CircuitBreaker
from .security import get_input_sanitizer, get_security_auditor

config = get_config()

class CodingAgent:
    """
    Main AI Coding Agent class that coordinates all specialized models
    and provides the primary interface for code generation tasks.
    """
    
    def __init__(self):
        """Initialize the coding agent with a unique session ID"""
        self.session_id = str(uuid.uuid4())
        self.projects_dir = config.PROJECTS_DIR
        self.projects_dir.mkdir(exist_ok=True)

        # Initialize logging
        self.logger = get_logger(__name__)

        # Initialize core components
        try:
            self.llm_manager = LLMManager()
            self.conversation_manager = ConversationManager()
            self.validator = CodeValidator()
            self.database = DatabaseManager()
            self.performance_monitor = get_performance_monitor(self.database)
            self.input_sanitizer = get_input_sanitizer()
            self.security_auditor = get_security_auditor()
            self.logger.info("All core components initialized successfully")
        except Exception as e:
            self.logger.warning(f"Component initialization failed: {e}. Running in limited mode.")
            self.llm_manager = None
            self.conversation_manager = ConversationManager()
            self.validator = CodeValidator()
            self.database = None
            self.performance_monitor = get_performance_monitor(None)
            self.input_sanitizer = get_input_sanitizer()
            self.security_auditor = get_security_auditor()

        self.logger.info(f"🤖 Coding Agent initialized (Session: {self.session_id[:8]})")
    
    def generate_code(self, task: str, language: str = "python", use_conversation_context: bool = True) -> str:
        """
        Generate code based on task description and target language

        Args:
            task: Description of what to build
            language: Target programming language
            use_conversation_context: Whether to use conversation history for context

        Returns:
            Generated code as string

        Raises:
            CodingAgentError: If code generation fails
        """
        try:
            # Sanitize inputs for security (no HTML escaping for code generation)
            task = self.input_sanitizer.sanitize_text_input(task, max_length=5000, escape_html=False)
            language = self.input_sanitizer.sanitize_text_input(language, max_length=50, escape_html=False)

            # Log security event
            self.security_auditor.log_security_event(
                "code_generation_request",
                {
                    "task_length": len(task),
                    "language": language,
                    "session_id": self.session_id
                },
                "info"
            )

            self.logger.info(f"Generating {language} code for: {task}")

            if not task or not task.strip():
                raise CodingAgentError("Task description cannot be empty", "INVALID_TASK")

            # Add user message to conversation
            if self.conversation_manager:
                self.conversation_manager.add_message_to_current("user", task, {
                    "language": language,
                    "task_type": "code_generation"
                })

            # Use LLM if available
            if self.llm_manager:
                try:
                    # Determine the appropriate role based on language
                    role = self._get_role_for_language(language)

                    # Build prompt with context if requested
                    prompt = self._build_code_generation_prompt(task, language, use_conversation_context)

                    # Generate response with performance monitoring
                    with self.performance_monitor.measure_operation(
                        "code_generation",
                        model_used=role,
                        language=language,
                        task_length=len(task)
                    ):
                        response = self.llm_manager.generate_response(prompt, role)

                    # Validate generated code with performance monitoring
                    with self.performance_monitor.measure_operation(
                        "code_validation",
                        language=language,
                        code_length=len(response)
                    ):
                        validated_response = self._validate_and_improve_code(response, language)

                    # Add assistant response to conversation
                    if self.conversation_manager:
                        self.conversation_manager.add_message_to_current("assistant", validated_response, {
                            "language": language,
                            "role": role,
                            "task_type": "code_generation",
                            "validated": True
                        })

                    return validated_response

                except Exception as llm_error:
                    self.logger.warning(f"LLM generation failed: {llm_error}. Using fallback.")

            # Fallback implementation
            fallback_response = f"# Generated {language} code for: {task}\n# LLM not available - using fallback implementation"

            # Add fallback response to conversation
            if self.conversation_manager:
                self.conversation_manager.add_message_to_current("assistant", fallback_response, {
                    "language": language,
                    "task_type": "code_generation",
                    "fallback": True
                })

            return fallback_response

        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            if isinstance(e, CodingAgentError):
                raise
            raise CodingAgentError(f"Unexpected error during code generation: {e}", "GENERATION_ERROR")

    def _get_role_for_language(self, language: str) -> str:
        """Get appropriate LLM role based on programming language"""
        language_lower = language.lower()

        if language_lower in ["html", "css", "javascript", "js", "typescript", "ts", "react", "vue", "angular"]:
            return "frontend"
        elif language_lower in ["python", "java", "c#", "go", "rust", "php", "ruby", "node", "nodejs"]:
            return "backend"
        else:
            return "assistant"

    def _build_code_generation_prompt(self, task: str, language: str, use_context: bool) -> str:
        """Build prompt for code generation with optional context"""
        base_prompt = f"Generate {language} code for the following task: {task}\n\nPlease provide clean, well-commented code that follows best practices."

        if not use_context or not self.conversation_manager:
            return base_prompt

        # Get conversation context
        current_conv = self.conversation_manager.get_current_conversation()
        if current_conv and len(current_conv.messages) > 1:  # More than just the current message
            context = current_conv.get_formatted_context(max_messages=10)  # Last 10 messages
            return f"Previous conversation context:\n{context}\n\n{base_prompt}"

        return base_prompt

    def _validate_and_improve_code(self, code: str, language: str) -> str:
        """
        Validate generated code and improve it if needed

        Args:
            code: Generated code to validate
            language: Programming language

        Returns:
            Validated and potentially improved code
        """
        try:
            # Validate the code
            if language.lower() == "python":
                is_valid, issues = self.validator.validate_python(code)
            elif language.lower() == "javascript":
                is_valid, issues = self.validator.validate_javascript(code)
            elif language.lower() in ["html", "htm"]:
                is_valid, issues = self.validator.validate_html(code)
            else:
                # For unsupported languages, just return the code
                self.logger.info(f"Validation not supported for {language}, returning code as-is")
                return code

            if is_valid:
                self.logger.info(f"Code validation passed for {language}")
                if issues:  # Warnings only
                    self.logger.info(f"Code has {len(issues)} warnings: {issues[:3]}")  # Log first 3 warnings
                return code
            else:
                # Code has errors - log them and return with comments
                self.logger.warning(f"Code validation failed for {language}: {issues}")
                error_comments = "\n".join([f"# VALIDATION ERROR: {issue}" for issue in issues[:5]])  # First 5 errors
                return f"{error_comments}\n\n{code}"

        except Exception as e:
            self.logger.error(f"Code validation failed with exception: {e}")
            return code  # Return original code if validation fails

    def get_system_health(self) -> dict:
        """Get current system health status"""
        try:
            health = self.performance_monitor.get_system_health()
            return {
                "status": health.overall_status,
                "timestamp": health.timestamp.isoformat(),
                "cpu_usage_percent": health.cpu_usage_percent,
                "memory_usage_percent": health.memory_usage_percent,
                "disk_usage_percent": health.disk_usage_percent,
                "ollama_status": health.ollama_status,
                "database_status": health.database_status,
                "uptime_seconds": health.uptime_seconds
            }
        except Exception as e:
            self.logger.error(f"Failed to get system health: {e}")
            return {"status": "unknown", "error": str(e)}

    def get_performance_stats(self, hours: int = 24) -> dict:
        """Get performance statistics for the last N hours"""
        try:
            return self.performance_monitor.get_performance_summary(hours)
        except Exception as e:
            self.logger.error(f"Failed to get performance stats: {e}")
            return {"error": str(e)}
