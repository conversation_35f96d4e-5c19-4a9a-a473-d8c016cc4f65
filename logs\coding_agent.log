2025-07-09 15:49:46,919 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: ef4dbd45)
2025-07-09 15:49:46,940 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: c5203508)
2025-07-09 15:49:46,941 - agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-09 15:49:46,943 - agent - INFO - agent.py:49 - Generating javascript code for: create a hello world function
2025-07-09 15:49:46,948 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 9c041892)
2025-07-09 15:49:46,952 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 8cd1ddc9)
2025-07-09 15:50:12,375 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 57376caf)
2025-07-09 15:50:12,391 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 8d411751)
2025-07-09 15:50:12,394 - agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-09 15:50:12,396 - agent - INFO - agent.py:49 - Generating javascript code for: create a hello world function
2025-07-09 15:50:12,405 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 2e2d1e00)
2025-07-09 15:50:12,410 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 6e786f88)
2025-07-09 15:50:39,489 - coding_agent - INFO - logger.py:78 - 🤖 AI Coding Agent Starting Up
2025-07-09 15:50:39,493 - coding_agent - INFO - logger.py:79 - Environment: development
2025-07-09 15:50:39,496 - coding_agent - INFO - logger.py:80 - Debug Mode: True
2025-07-09 15:50:39,498 - coding_agent - INFO - logger.py:81 - Log Level: DEBUG
2025-07-09 15:50:39,502 - coding_agent - INFO - logger.py:82 - Log File: E:\codingagent\logs\coding_agent.log
2025-07-09 15:50:39,505 - coding_agent - INFO - logger.py:83 - Ollama Host: http://localhost:11434
2025-07-09 15:50:39,512 - src.agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: b644ddd1)
2025-07-09 15:50:39,528 - src.agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-09 15:50:39,592 - src.agent - INFO - agent.py:49 - Generating python code for: 
2025-07-09 15:50:39,607 - src.agent - ERROR - agent.py:57 - Code generation failed: Task description cannot be empty
2025-07-09 15:50:39,613 - coding_agent - INFO - logger.py:87 - 🛑 AI Coding Agent Shutting Down
2025-07-09 23:59:59,396 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-09 23:59:59,843 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:00,271 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:00,699 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:02,742 - models - INFO - models.py:92 - Ollama connection successful. Available models: 4
2025-07-10 00:00:02,748 - models - ERROR - models.py:111 - Failed to get available models: 'name'
2025-07-10 00:00:03,163 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:05,216 - models - INFO - models.py:92 - Ollama connection successful. Available models: 4
2025-07-10 00:00:05,218 - models - INFO - models.py:138 - Generating response with phi3:mini for role: assistant
2025-07-10 00:00:39,382 - models - INFO - models.py:158 - Response generated in 34.16s, length: 6 chars
2025-07-10 00:00:39,813 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:39,814 - models - INFO - models.py:138 - Generating response with phi3:mini for role: assistant
2025-07-10 00:00:41,877 - models - INFO - models.py:158 - Response generated in 2.06s, length: 0 chars
2025-07-10 00:01:10,854 - coding_agent - INFO - logger.py:78 - 🤖 AI Coding Agent Starting Up
2025-07-10 00:01:10,874 - coding_agent - INFO - logger.py:79 - Environment: development
2025-07-10 00:01:10,877 - coding_agent - INFO - logger.py:80 - Debug Mode: True
2025-07-10 00:01:10,879 - coding_agent - INFO - logger.py:81 - Log Level: DEBUG
2025-07-10 00:01:10,881 - coding_agent - INFO - logger.py:82 - Log File: E:\codingagent\logs\coding_agent.log
2025-07-10 00:01:10,883 - coding_agent - INFO - logger.py:83 - Ollama Host: http://localhost:11434
2025-07-10 00:01:13,110 - src.models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:01:15,185 - src.models - INFO - models.py:92 - Ollama connection successful. Available models: 4
2025-07-10 00:01:15,192 - src.models - ERROR - models.py:111 - Failed to get available models: 'name'
2025-07-10 00:01:15,195 - src.models - INFO - models.py:138 - Generating response with phi3:mini for role: backend
2025-07-10 00:01:30,423 - src.models - INFO - models.py:158 - Response generated in 15.23s, length: 179 chars
2025-07-10 00:01:30,426 - src.models - INFO - models.py:138 - Generating response with phi3:mini for role: assistant
2025-07-10 00:05:18,090 - src.models - INFO - models.py:158 - Response generated in 227.66s, length: 4740 chars
2025-07-10 00:05:18,094 - src.models - INFO - models.py:138 - Generating response with phi3:mini for role: frontend
2025-07-10 00:05:35,802 - src.models - INFO - models.py:158 - Response generated in 17.71s, length: 302 chars
2025-07-10 00:05:35,805 - coding_agent - INFO - logger.py:87 - 🛑 AI Coding Agent Shutting Down
2025-07-10 00:09:38,755 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 1ba45e33)
2025-07-10 00:09:38,763 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: c0ae8f41)
2025-07-10 00:09:38,765 - agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-10 00:09:38,766 - agent - INFO - agent.py:49 - Generating javascript code for: create a hello world function
2025-07-10 00:09:38,770 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 7f2a8e57)
2025-07-10 00:09:38,772 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 1e42c125)
2025-07-10 00:09:40,392 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:40,831 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:41,290 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:41,718 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:43,752 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:09:43,758 - agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:09:44,196 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:46,227 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:09:46,229 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:09:48,313 - agent - INFO - models.py:165 - Response generated in 2.08s, length: 6 chars
2025-07-10 00:09:48,753 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:48,754 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:09:50,781 - agent - INFO - models.py:165 - Response generated in 2.03s, length: 0 chars
2025-07-10 00:11:13,767 - conversation - INFO - conversation.py:179 - Created new conversation: 843a6479-8d3e-4b4b-970c-375b49d51414 - Test Conversation
2025-07-10 00:11:13,782 - conversation - INFO - conversation.py:179 - Created new conversation: a2aab9ea-141f-46b0-bb8f-436ae4305034 - First
2025-07-10 00:11:13,784 - conversation - INFO - conversation.py:179 - Created new conversation: c8795bfa-5cbe-49b4-9596-6f944a8da1d6 - Second
2025-07-10 00:11:13,787 - conversation - INFO - conversation.py:196 - Switched to conversation: a2aab9ea-141f-46b0-bb8f-436ae4305034
2025-07-10 00:11:13,794 - conversation - INFO - conversation.py:179 - Created new conversation: 8a21beac-c5b6-4d4e-9e90-293e2ca37b9a - New Conversation
2025-07-10 00:11:13,799 - conversation - INFO - conversation.py:208 - Added user message to conversation 8a21beac-c5b6-4d4e-9e90-293e2ca37b9a
2025-07-10 00:11:13,806 - conversation - INFO - conversation.py:179 - Created new conversation: 21a69463-9d74-4269-bb7c-c952308c9a68 - First
2025-07-10 00:11:13,808 - conversation - INFO - conversation.py:179 - Created new conversation: 3813913c-01e0-4b04-8001-2e490f52dbd3 - Second
2025-07-10 00:11:13,810 - conversation - INFO - conversation.py:196 - Switched to conversation: 21a69463-9d74-4269-bb7c-c952308c9a68
2025-07-10 00:11:13,814 - conversation - INFO - conversation.py:208 - Added user message to conversation 21a69463-9d74-4269-bb7c-c952308c9a68
2025-07-10 00:11:13,819 - conversation - INFO - conversation.py:179 - Created new conversation: cf4b5a84-8fd8-42c3-824e-6722e0a32eeb - To Delete
2025-07-10 00:11:13,822 - conversation - INFO - conversation.py:236 - Deleted conversation: cf4b5a84-8fd8-42c3-824e-6722e0a32eeb
2025-07-10 00:12:25,260 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:12:25,268 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:12:25,270 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 7f4f5da8)
2025-07-10 00:12:25,708 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:12:25,709 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:12:25,711 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: c6f80a44)
2025-07-10 00:12:25,712 - agent - INFO - agent.py:61 - Generating python code for: create a hello world function
2025-07-10 00:12:25,716 - agent - INFO - conversation.py:179 - Created new conversation: 9fbde049-a697-4c0c-b1a4-2c39af4f320f - New Conversation
2025-07-10 00:12:25,717 - agent - INFO - conversation.py:208 - Added user message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:12:25,719 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:13:14,885 - agent - INFO - models.py:165 - Response generated in 49.16s, length: 603 chars
2025-07-10 00:13:14,887 - agent - INFO - conversation.py:208 - Added assistant message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:13:14,889 - agent - INFO - agent.py:61 - Generating javascript code for: create a hello world function
2025-07-10 00:13:14,890 - agent - INFO - conversation.py:208 - Added user message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:13:14,892 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 00:14:18,588 - agent - INFO - models.py:165 - Response generated in 63.70s, length: 695 chars
2025-07-10 00:14:18,590 - agent - INFO - conversation.py:208 - Added assistant message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:14:19,012 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:19,015 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:14:19,017 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 5821ccbb)
2025-07-10 00:14:19,421 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:19,423 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:14:19,424 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 38e6d592)
2025-07-10 00:14:19,466 - agent - INFO - conversation.py:179 - Created new conversation: c03ce521-f936-4292-8c05-e148f01abe0e - Test Conversation
2025-07-10 00:14:19,472 - agent - INFO - conversation.py:179 - Created new conversation: 55bd652d-ab96-4998-a5f7-3887c4be6bd1 - First
2025-07-10 00:14:19,473 - agent - INFO - conversation.py:179 - Created new conversation: 57197253-0921-49bb-956e-6d36d8facfdf - Second
2025-07-10 00:14:19,475 - agent - INFO - conversation.py:196 - Switched to conversation: 55bd652d-ab96-4998-a5f7-3887c4be6bd1
2025-07-10 00:14:19,489 - agent - INFO - conversation.py:179 - Created new conversation: 68187bdb-3e7b-4a96-a426-3da54a46a91d - New Conversation
2025-07-10 00:14:19,492 - agent - INFO - conversation.py:208 - Added user message to conversation 68187bdb-3e7b-4a96-a426-3da54a46a91d
2025-07-10 00:14:19,497 - agent - INFO - conversation.py:179 - Created new conversation: 2e7c9faa-70dd-4d08-a895-e501242616bb - First
2025-07-10 00:14:19,504 - agent - INFO - conversation.py:179 - Created new conversation: 7691bbc1-aaa3-4955-a20d-bb27bc2dc9eb - Second
2025-07-10 00:14:19,506 - agent - INFO - conversation.py:196 - Switched to conversation: 2e7c9faa-70dd-4d08-a895-e501242616bb
2025-07-10 00:14:19,507 - agent - INFO - conversation.py:208 - Added user message to conversation 2e7c9faa-70dd-4d08-a895-e501242616bb
2025-07-10 00:14:19,513 - agent - INFO - conversation.py:179 - Created new conversation: 5328a45a-6a8b-43e1-875e-5d3cc51aaeee - To Delete
2025-07-10 00:14:19,519 - agent - INFO - conversation.py:236 - Deleted conversation: 5328a45a-6a8b-43e1-875e-5d3cc51aaeee
2025-07-10 00:14:19,943 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:20,368 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:20,865 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:21,276 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:23,286 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:14:23,291 - agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:14:23,728 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:25,771 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:14:25,772 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:14:27,817 - agent - INFO - models.py:165 - Response generated in 2.04s, length: 6 chars
2025-07-10 00:14:28,232 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:28,233 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:14:30,259 - agent - INFO - models.py:165 - Response generated in 2.02s, length: 0 chars
2025-07-10 00:15:54,893 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:55,344 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:55,772 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:56,206 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:58,251 - models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:15:58,256 - models - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:15:58,694 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:16:00,721 - models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:16:00,722 - models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:16:01,426 - models - INFO - models.py:165 - Response generated in 0.70s, length: 6 chars
2025-07-10 00:16:01,859 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:16:01,860 - models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:16:03,882 - models - INFO - models.py:165 - Response generated in 2.02s, length: 0 chars
2025-07-10 00:17:32,794 - conversation - INFO - conversation.py:179 - Created new conversation: 4efc0d5e-48dd-4d01-9cdf-69ad94f1d816 - Test Conversation
2025-07-10 00:17:32,811 - conversation - INFO - conversation.py:179 - Created new conversation: 2b1219c6-da00-439c-9120-1f0cedbd3465 - First
2025-07-10 00:17:32,813 - conversation - INFO - conversation.py:179 - Created new conversation: 614f96dd-c40f-44e7-be24-a3739d86b025 - Second
2025-07-10 00:17:32,819 - conversation - INFO - conversation.py:196 - Switched to conversation: 2b1219c6-da00-439c-9120-1f0cedbd3465
2025-07-10 00:17:32,826 - conversation - INFO - conversation.py:179 - Created new conversation: 5eb4e027-c3d8-46d2-8268-150cd4ff9541 - New Conversation
2025-07-10 00:17:32,829 - conversation - INFO - conversation.py:208 - Added user message to conversation 5eb4e027-c3d8-46d2-8268-150cd4ff9541
2025-07-10 00:17:32,835 - conversation - INFO - conversation.py:179 - Created new conversation: ee14439e-1603-4044-8544-d8d98b720eda - First
2025-07-10 00:17:32,842 - conversation - INFO - conversation.py:179 - Created new conversation: 35183fcb-73f1-44fc-a825-ba8702baf3bc - Second
2025-07-10 00:17:32,843 - conversation - INFO - conversation.py:196 - Switched to conversation: ee14439e-1603-4044-8544-d8d98b720eda
2025-07-10 00:17:32,844 - conversation - INFO - conversation.py:208 - Added user message to conversation ee14439e-1603-4044-8544-d8d98b720eda
2025-07-10 00:17:32,848 - conversation - INFO - conversation.py:179 - Created new conversation: 2bb9a96f-472c-43c9-a00a-462441a83389 - To Delete
2025-07-10 00:17:32,854 - conversation - INFO - conversation.py:236 - Deleted conversation: 2bb9a96f-472c-43c9-a00a-462441a83389
2025-07-10 00:17:45,920 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:17:57,793 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:17:59,844 - models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:17:59,849 - models - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:18:10,727 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:18:10,734 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:18:10,740 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: df82778f)
2025-07-10 00:18:11,176 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:18:11,177 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:18:11,178 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 6cb0d027)
2025-07-10 00:18:11,180 - agent - INFO - agent.py:61 - Generating python code for: create a hello world function
2025-07-10 00:18:11,182 - agent - INFO - conversation.py:179 - Created new conversation: c03d6df6-a808-4e61-b432-7ff55b6ea2a9 - New Conversation
2025-07-10 00:18:11,186 - agent - INFO - conversation.py:208 - Added user message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:18:11,187 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:18:52,856 - agent - INFO - models.py:165 - Response generated in 41.67s, length: 612 chars
2025-07-10 00:18:52,859 - agent - INFO - conversation.py:208 - Added assistant message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:18:52,861 - agent - INFO - agent.py:61 - Generating javascript code for: create a hello world function
2025-07-10 00:18:52,863 - agent - INFO - conversation.py:208 - Added user message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:18:52,864 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 00:20:29,125 - agent - INFO - models.py:165 - Response generated in 96.26s, length: 1300 chars
2025-07-10 00:20:29,127 - agent - INFO - conversation.py:208 - Added assistant message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:20:29,541 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:29,542 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:20:29,543 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: c30d3ff1)
2025-07-10 00:20:29,947 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:29,949 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:20:29,951 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 51994a00)
2025-07-10 00:20:29,990 - agent - INFO - conversation.py:179 - Created new conversation: 07f09943-8ac3-43e4-a28e-75ad8315ddcc - Test Conversation
2025-07-10 00:20:29,994 - agent - INFO - conversation.py:179 - Created new conversation: 626bf250-7b6c-455e-8bb5-7aec95edb4c0 - First
2025-07-10 00:20:29,999 - agent - INFO - conversation.py:179 - Created new conversation: c35c14de-daa5-450e-b3cd-73a4eec97281 - Second
2025-07-10 00:20:30,003 - agent - INFO - conversation.py:196 - Switched to conversation: 626bf250-7b6c-455e-8bb5-7aec95edb4c0
2025-07-10 00:20:30,008 - agent - INFO - conversation.py:179 - Created new conversation: 8d03adb9-a8b9-4066-a2b9-0d44b4e63c75 - New Conversation
2025-07-10 00:20:30,010 - agent - INFO - conversation.py:208 - Added user message to conversation 8d03adb9-a8b9-4066-a2b9-0d44b4e63c75
2025-07-10 00:20:30,015 - agent - INFO - conversation.py:179 - Created new conversation: fd627676-f99b-4c0d-84ae-21df1fdf5c63 - First
2025-07-10 00:20:30,018 - agent - INFO - conversation.py:179 - Created new conversation: 88592907-6542-43c1-aae8-bb62f7a79ce1 - Second
2025-07-10 00:20:30,021 - agent - INFO - conversation.py:196 - Switched to conversation: fd627676-f99b-4c0d-84ae-21df1fdf5c63
2025-07-10 00:20:30,022 - agent - INFO - conversation.py:208 - Added user message to conversation fd627676-f99b-4c0d-84ae-21df1fdf5c63
2025-07-10 00:20:30,027 - agent - INFO - conversation.py:179 - Created new conversation: 4e692843-1abe-46d8-b3cc-fec84df80b87 - To Delete
2025-07-10 00:20:30,031 - agent - INFO - conversation.py:236 - Deleted conversation: 4e692843-1abe-46d8-b3cc-fec84df80b87
2025-07-10 00:20:30,468 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:30,884 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:31,311 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:31,724 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:33,743 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:20:33,749 - agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:20:34,202 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:36,239 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:20:36,240 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:20:38,232 - agent - INFO - models.py:165 - Response generated in 1.99s, length: 6 chars
2025-07-10 00:20:38,641 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:38,642 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:20:40,694 - agent - INFO - models.py:165 - Response generated in 2.05s, length: 0 chars
2025-07-10 00:23:06,280 - coding_agent - INFO - logger.py:76 - 🤖 AI Coding Agent Starting Up
2025-07-10 00:23:06,286 - coding_agent - INFO - logger.py:77 - Environment: development
2025-07-10 00:23:06,289 - coding_agent - INFO - logger.py:78 - Debug Mode: True
2025-07-10 00:23:06,291 - coding_agent - INFO - logger.py:79 - Log Level: DEBUG
2025-07-10 00:23:06,292 - coding_agent - INFO - logger.py:80 - Log File: E:\codingagent\logs\coding_agent.log
2025-07-10 00:23:06,294 - coding_agent - INFO - logger.py:81 - Ollama Host: http://localhost:11434
2025-07-10 00:23:06,296 - coding_agent - INFO - <string>:1 - Testing logging infrastructure
2025-07-10 00:23:06,297 - coding_agent - WARNING - <string>:1 - This is a warning test
2025-07-10 00:23:06,299 - coding_agent - ERROR - <string>:1 - This is an error test
2025-07-10 00:24:59,649 - src.models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:25:01,683 - src.models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:25:01,689 - src.models - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:25:12,289 - src.models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:25:12,297 - src.models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:25:20,307 - src.models - INFO - models.py:165 - Response generated in 8.00s, length: 64 chars
2025-07-10 00:26:11,692 - src.conversation - INFO - conversation.py:176 - Created new conversation: 128a2523-d453-44d1-abda-5715702c6b49 - Test Conversation
2025-07-10 00:26:11,696 - src.conversation - INFO - conversation.py:205 - Added user message to conversation 128a2523-d453-44d1-abda-5715702c6b49
2025-07-10 00:26:11,698 - src.conversation - INFO - conversation.py:205 - Added assistant message to conversation 128a2523-d453-44d1-abda-5715702c6b49
2025-07-10 00:26:39,009 - src.models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:26:39,018 - src.models - INFO - conversation.py:176 - Created new conversation: 94aee67c-616c-413c-b937-88a44f9f5f3e - Test
2025-07-10 00:26:39,024 - src.models - INFO - conversation.py:205 - Added user message to conversation 94aee67c-616c-413c-b937-88a44f9f5f3e
2025-07-10 00:26:39,026 - src.models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:26:48,817 - src.models - INFO - models.py:165 - Response generated in 9.79s, length: 50 chars
2025-07-10 00:29:49,587 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:29:49,622 - src.database - INFO - database.py:162 - Created conversation: test-123 - Test Conversation
2025-07-10 00:29:49,668 - src.database - DEBUG - database.py:269 - Added user message to conversation test-123
2025-07-10 00:30:19,602 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:30:31,348 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:32:09,287 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:32:09,336 - src.database - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:32:26,431 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,166 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,372 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,382 - src.database - INFO - database.py:162 - Created conversation: c25f7f25-6b1e-44ac-b89e-7b4881738e44 - Test Conversation
2025-07-10 00:34:24,400 - src.database - INFO - database.py:225 - Deleted conversation: c25f7f25-6b1e-44ac-b89e-7b4881738e44
2025-07-10 00:34:24,468 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,476 - src.database - INFO - database.py:162 - Created conversation: f6ab4648-e015-44a4-b53d-b754dd477f90 - Test Conversation
2025-07-10 00:34:24,486 - src.database - DEBUG - database.py:269 - Added user message to conversation f6ab4648-e015-44a4-b53d-b754dd477f90
2025-07-10 00:34:24,495 - src.database - DEBUG - database.py:269 - Added assistant message to conversation f6ab4648-e015-44a4-b53d-b754dd477f90
2025-07-10 00:34:24,571 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,579 - src.database - INFO - database.py:162 - Created conversation: e98309f1-5637-4907-81c7-8e2e957e46ed - Test Conversation
2025-07-10 00:34:24,588 - src.database - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:34:24,668 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,677 - src.database - INFO - database.py:162 - Created conversation: a3fbc958-c1f4-4ec8-945d-d45db8804a66 - Test Conversation
2025-07-10 00:34:24,686 - src.database - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:34:24,753 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,840 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:36:33,276 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:36:33,297 - src.database - INFO - database.py:162 - Created conversation: d37a2daa-9b5a-40d2-854f-abc70561aebf - Test Conversation
2025-07-10 00:36:33,355 - src.database - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:36:51,906 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:36:51,924 - src.database - INFO - database.py:162 - Created conversation: 4cd19e4b-625f-4d22-97be-495a3405e69d - Test Conversation
2025-07-10 00:36:51,937 - src.database - DEBUG - database.py:269 - Added user message to conversation 4cd19e4b-625f-4d22-97be-495a3405e69d
2025-07-10 00:36:51,950 - src.database - DEBUG - database.py:269 - Added assistant message to conversation 4cd19e4b-625f-4d22-97be-495a3405e69d
2025-07-10 00:37:13,817 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:37:13,832 - src.database - INFO - database.py:162 - Created conversation: 81f8fad7-2101-4c3f-9861-f3fc16c3f614 - Test Conversation
2025-07-10 00:37:13,843 - src.database - DEBUG - database.py:269 - Added user message to conversation 81f8fad7-2101-4c3f-9861-f3fc16c3f614
2025-07-10 00:37:13,854 - src.database - DEBUG - database.py:269 - Added assistant message to conversation 81f8fad7-2101-4c3f-9861-f3fc16c3f614
2025-07-10 00:38:50,341 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,401 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,410 - src.database - INFO - database.py:162 - Created conversation: 9158db64-42ba-47d1-aee7-e1dd9fab1ece - Test Conversation
2025-07-10 00:38:50,427 - src.database - INFO - database.py:225 - Deleted conversation: 9158db64-42ba-47d1-aee7-e1dd9fab1ece
2025-07-10 00:38:50,482 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,493 - src.database - INFO - database.py:162 - Created conversation: e72356f1-776f-4a75-b503-b0ce1c9dcc1e - Test Conversation
2025-07-10 00:38:50,505 - src.database - DEBUG - database.py:269 - Added user message to conversation e72356f1-776f-4a75-b503-b0ce1c9dcc1e
2025-07-10 00:38:50,515 - src.database - DEBUG - database.py:269 - Added assistant message to conversation e72356f1-776f-4a75-b503-b0ce1c9dcc1e
2025-07-10 00:38:50,589 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,599 - src.database - INFO - database.py:162 - Created conversation: c8ecc544-343a-4d38-9dae-1d547de6e81c - Test Conversation
2025-07-10 00:38:50,609 - src.database - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:38:50,673 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,682 - src.database - INFO - database.py:162 - Created conversation: de61b2ae-d512-46f5-acdf-cb1c87dd0ebb - Test Conversation
2025-07-10 00:38:50,692 - src.database - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:38:50,759 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,825 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:39:46,059 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:39:46,068 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:39:46,074 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 5b7cf4ee)
2025-07-10 00:39:46,500 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:39:46,502 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:39:46,503 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 81833660)
2025-07-10 00:39:46,505 - src.agent - INFO - agent.py:61 - Generating python code for: create a hello world function
2025-07-10 00:39:46,506 - src.agent - INFO - conversation.py:176 - Created new conversation: d16dd758-9d29-4f7d-ac1a-b181031b3615 - New Conversation
2025-07-10 00:39:46,508 - src.agent - INFO - conversation.py:205 - Added user message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:39:46,509 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:40:24,133 - src.agent - INFO - models.py:165 - Response generated in 37.62s, length: 512 chars
2025-07-10 00:40:24,134 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:40:24,136 - src.agent - INFO - agent.py:61 - Generating javascript code for: create a hello world function
2025-07-10 00:40:24,137 - src.agent - INFO - conversation.py:205 - Added user message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:40:24,139 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 00:41:31,086 - src.agent - INFO - models.py:165 - Response generated in 66.95s, length: 757 chars
2025-07-10 00:41:31,088 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:41:31,510 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:31,511 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:41:31,513 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 482bc806)
2025-07-10 00:41:31,909 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:31,911 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:41:31,912 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 8c3e4755)
2025-07-10 00:41:31,955 - src.agent - INFO - conversation.py:176 - Created new conversation: dcb02380-ba23-453c-9e2a-9d2b614cfff9 - Test Conversation
2025-07-10 00:41:31,959 - src.agent - INFO - conversation.py:176 - Created new conversation: f4b18a97-d893-4284-91d4-e450379b4d43 - First
2025-07-10 00:41:31,961 - src.agent - INFO - conversation.py:176 - Created new conversation: 8f54198a-df7e-4afa-801f-11394329e85c - Second
2025-07-10 00:41:31,965 - src.agent - INFO - conversation.py:193 - Switched to conversation: f4b18a97-d893-4284-91d4-e450379b4d43
2025-07-10 00:41:31,971 - src.agent - INFO - conversation.py:176 - Created new conversation: f917be8f-16cb-48d6-ba3f-6e4f93017f0d - New Conversation
2025-07-10 00:41:31,973 - src.agent - INFO - conversation.py:205 - Added user message to conversation f917be8f-16cb-48d6-ba3f-6e4f93017f0d
2025-07-10 00:41:31,977 - src.agent - INFO - conversation.py:176 - Created new conversation: 5c4cb202-6fe9-4ff8-91a5-c7d5a082c19a - First
2025-07-10 00:41:31,982 - src.agent - INFO - conversation.py:176 - Created new conversation: 003b009f-9eb1-4fe8-ab1d-b2b25cde4144 - Second
2025-07-10 00:41:31,985 - src.agent - INFO - conversation.py:193 - Switched to conversation: 5c4cb202-6fe9-4ff8-91a5-c7d5a082c19a
2025-07-10 00:41:31,987 - src.agent - INFO - conversation.py:205 - Added user message to conversation 5c4cb202-6fe9-4ff8-91a5-c7d5a082c19a
2025-07-10 00:41:31,992 - src.agent - INFO - conversation.py:176 - Created new conversation: 27ada4b8-2852-4f65-9ec0-cf3c3153fe9b - To Delete
2025-07-10 00:41:31,994 - src.agent - INFO - conversation.py:233 - Deleted conversation: 27ada4b8-2852-4f65-9ec0-cf3c3153fe9b
2025-07-10 00:41:32,099 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,224 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,237 - src.agent - INFO - database.py:162 - Created conversation: 6630439a-28bb-4828-8166-920a3466cacd - Test Conversation
2025-07-10 00:41:32,256 - src.agent - INFO - database.py:225 - Deleted conversation: 6630439a-28bb-4828-8166-920a3466cacd
2025-07-10 00:41:32,313 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,322 - src.agent - INFO - database.py:162 - Created conversation: 323ea8ee-ca41-4a85-8ac6-c9dafda1cbf5 - Test Conversation
2025-07-10 00:41:32,337 - src.agent - DEBUG - database.py:269 - Added user message to conversation 323ea8ee-ca41-4a85-8ac6-c9dafda1cbf5
2025-07-10 00:41:32,346 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation 323ea8ee-ca41-4a85-8ac6-c9dafda1cbf5
2025-07-10 00:41:32,417 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,425 - src.agent - INFO - database.py:162 - Created conversation: 3247b566-c09a-41a6-80bf-416cb9f8422a - Test Conversation
2025-07-10 00:41:32,435 - src.agent - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:41:32,492 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,501 - src.agent - INFO - database.py:162 - Created conversation: 7a5c6506-4286-4a37-a9a7-d868b96053ad - Test Conversation
2025-07-10 00:41:32,510 - src.agent - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:41:32,574 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,638 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:33,055 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:33,474 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:33,899 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:34,339 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:36,377 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:41:36,385 - src.agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:41:36,811 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:38,845 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:41:38,847 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:41:40,894 - src.agent - INFO - models.py:165 - Response generated in 2.05s, length: 6 chars
2025-07-10 00:41:41,315 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:41,316 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:41:43,354 - src.agent - INFO - models.py:165 - Response generated in 2.04s, length: 0 chars
2025-07-10 00:53:46,242 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:53:46,259 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:53:46,263 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 00:53:46,266 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 782aae61)
2025-07-10 00:53:46,683 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:53:46,689 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:53:46,692 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 00:53:46,695 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 2d72b255)
2025-07-10 00:53:46,697 - src.agent - INFO - agent.py:67 - Generating python code for: create a hello world function
2025-07-10 00:53:46,700 - src.agent - INFO - conversation.py:176 - Created new conversation: befc0e28-15fe-413f-8055-e0fce529e89d - New Conversation
2025-07-10 00:53:46,705 - src.agent - INFO - conversation.py:205 - Added user message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 00:53:46,708 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:57:05,051 - src.agent - INFO - models.py:165 - Response generated in 198.34s, length: 3955 chars
2025-07-10 00:57:05,056 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 12) (<unknown>, line 12)
2025-07-10 00:57:05,062 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 12) (<unknown>, line 12)']
2025-07-10 00:57:05,069 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 00:57:05,073 - src.agent - INFO - agent.py:67 - Generating javascript code for: create a hello world function
2025-07-10 00:57:05,078 - src.agent - INFO - conversation.py:205 - Added user message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 00:57:05,085 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 01:03:47,019 - src.agent - INFO - models.py:165 - Response generated in 401.93s, length: 4976 chars
2025-07-10 01:03:47,023 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 01:03:47,026 - src.agent - INFO - agent.py:178 - Code validation passed for javascript
2025-07-10 01:03:47,029 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 01:03:47,542 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:47,548 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:47,554 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:47,559 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: a56294df)
2025-07-10 01:03:48,011 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:48,015 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,017 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:48,023 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 20b6dbe0)
2025-07-10 01:03:48,224 - src.agent - INFO - conversation.py:176 - Created new conversation: 811aed18-5bb9-4507-bfe3-f91f72809cc7 - Test Conversation
2025-07-10 01:03:48,251 - src.agent - INFO - conversation.py:176 - Created new conversation: 1769068e-32d6-47b9-9d75-9f1817b1491b - First
2025-07-10 01:03:48,263 - src.agent - INFO - conversation.py:176 - Created new conversation: db24684a-2a9c-4e95-b509-ca135b47a627 - Second
2025-07-10 01:03:48,268 - src.agent - INFO - conversation.py:193 - Switched to conversation: 1769068e-32d6-47b9-9d75-9f1817b1491b
2025-07-10 01:03:48,280 - src.agent - INFO - conversation.py:176 - Created new conversation: 6452ed1c-adfd-42b8-ac93-1aac7ebfb59c - New Conversation
2025-07-10 01:03:48,284 - src.agent - INFO - conversation.py:205 - Added user message to conversation 6452ed1c-adfd-42b8-ac93-1aac7ebfb59c
2025-07-10 01:03:48,297 - src.agent - INFO - conversation.py:176 - Created new conversation: 861d1819-f38e-4c5f-9d6d-5ab92e978b54 - First
2025-07-10 01:03:48,305 - src.agent - INFO - conversation.py:176 - Created new conversation: a976e860-3fdc-43f1-8ca6-d8b28b5a54a7 - Second
2025-07-10 01:03:48,309 - src.agent - INFO - conversation.py:193 - Switched to conversation: 861d1819-f38e-4c5f-9d6d-5ab92e978b54
2025-07-10 01:03:48,313 - src.agent - INFO - conversation.py:205 - Added user message to conversation 861d1819-f38e-4c5f-9d6d-5ab92e978b54
2025-07-10 01:03:48,324 - src.agent - INFO - conversation.py:176 - Created new conversation: 40e1b344-1a44-43f4-97ae-ea8ac0cdf060 - To Delete
2025-07-10 01:03:48,328 - src.agent - INFO - conversation.py:233 - Deleted conversation: 40e1b344-1a44-43f4-97ae-ea8ac0cdf060
2025-07-10 01:03:48,392 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,450 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,463 - src.agent - INFO - database.py:162 - Created conversation: 8240c282-8a97-4d38-a844-8f36b9861c7a - Test Conversation
2025-07-10 01:03:48,484 - src.agent - INFO - database.py:225 - Deleted conversation: 8240c282-8a97-4d38-a844-8f36b9861c7a
2025-07-10 01:03:48,550 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,564 - src.agent - INFO - database.py:162 - Created conversation: ba259f1d-b558-4d24-9533-85ecbdacb9a1 - Test Conversation
2025-07-10 01:03:48,575 - src.agent - DEBUG - database.py:269 - Added user message to conversation ba259f1d-b558-4d24-9533-85ecbdacb9a1
2025-07-10 01:03:48,586 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation ba259f1d-b558-4d24-9533-85ecbdacb9a1
2025-07-10 01:03:48,662 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,674 - src.agent - INFO - database.py:162 - Created conversation: 418e62b0-e83e-4b13-8d13-fd18f7a33665 - Test Conversation
2025-07-10 01:03:48,684 - src.agent - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 01:03:48,747 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,761 - src.agent - INFO - database.py:162 - Created conversation: a80bba0f-ca58-4b13-b3ff-c4b33330fbbe - Test Conversation
2025-07-10 01:03:48,771 - src.agent - INFO - database.py:397 - Saved project: Test Project
2025-07-10 01:03:48,836 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,910 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:49,332 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:49,336 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:49,340 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:49,342 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 809e73d0)
2025-07-10 01:03:49,774 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:49,778 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:49,780 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:49,783 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: faaf283f)
2025-07-10 01:03:49,786 - src.agent - INFO - agent.py:67 - Generating python code for: create a simple hello world function
2025-07-10 01:03:49,789 - src.agent - INFO - conversation.py:176 - Created new conversation: 3b359208-6a62-4658-9015-5442ebe4f386 - New Conversation
2025-07-10 01:03:49,793 - src.agent - INFO - conversation.py:205 - Added user message to conversation 3b359208-6a62-4658-9015-5442ebe4f386
2025-07-10 01:03:49,796 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:04:56,443 - src.agent - INFO - models.py:165 - Response generated in 66.64s, length: 1022 chars
2025-07-10 01:04:56,448 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 15) (<unknown>, line 15)
2025-07-10 01:04:56,451 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 15) (<unknown>, line 15)']
2025-07-10 01:04:56,457 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 3b359208-6a62-4658-9015-5442ebe4f386
2025-07-10 01:04:56,915 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:04:56,919 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:04:56,923 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:04:56,925 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 648cf171)
2025-07-10 01:04:56,929 - src.agent - INFO - agent.py:67 - Generating python code for: create a function that adds two numbers
2025-07-10 01:04:56,931 - src.agent - INFO - conversation.py:176 - Created new conversation: 384aa859-39a8-465b-9cfe-d6d9c56a4ded - New Conversation
2025-07-10 01:04:56,934 - src.agent - INFO - conversation.py:205 - Added user message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:04:56,937 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:05:31,001 - src.agent - INFO - models.py:165 - Response generated in 34.06s, length: 414 chars
2025-07-10 01:05:31,005 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 01:05:31,007 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 01:05:31,011 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:05:31,016 - src.agent - INFO - agent.py:67 - Generating python code for: now create a function that multiplies two numbers
2025-07-10 01:05:31,020 - src.agent - INFO - conversation.py:205 - Added user message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:05:31,023 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:07:24,618 - src.agent - INFO - models.py:165 - Response generated in 113.59s, length: 1331 chars
2025-07-10 01:07:24,624 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 01:07:24,627 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 01:07:24,631 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:07:25,068 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:07:25,074 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:07:25,077 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:07:25,079 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: bbf4a1e4)
2025-07-10 01:07:25,082 - src.agent - INFO - agent.py:67 - Generating python code for: create a test function
2025-07-10 01:07:25,085 - src.agent - INFO - conversation.py:176 - Created new conversation: 813429ef-f3ff-4d53-80a2-df8896048f4f - New Conversation
2025-07-10 01:07:25,089 - src.agent - INFO - conversation.py:205 - Added user message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:07:25,091 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:09:45,594 - src.agent - INFO - models.py:165 - Response generated in 140.50s, length: 2865 chars
2025-07-10 01:09:45,598 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 28) (<unknown>, line 28)
2025-07-10 01:09:45,602 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 28) (<unknown>, line 28)']
2025-07-10 01:09:45,606 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:09:45,664 - src.agent - INFO - database.py:162 - Created conversation: 813429ef-f3ff-4d53-80a2-df8896048f4f - create a test function
2025-07-10 01:09:45,701 - src.agent - DEBUG - database.py:269 - Added user message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:09:45,739 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:09:46,144 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:09:46,149 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:09:46,152 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:09:46,154 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: cd2f0a6f)
2025-07-10 01:09:46,158 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:09:46,163 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:09:46,166 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 01:09:46,168 - src.agent - INFO - validators.py:98 - HTML validation completed
2025-07-10 01:09:46,558 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:09:46,562 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:09:46,565 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:09:46,577 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: e3404b2f)
2025-07-10 01:09:46,580 - src.agent - INFO - agent.py:67 - Generating python code for: My name is Alice
2025-07-10 01:09:46,583 - src.agent - INFO - conversation.py:176 - Created new conversation: bf6d0fb5-d54a-4971-bf9a-282ea22e7c67 - New Conversation
2025-07-10 01:09:46,586 - src.agent - INFO - conversation.py:205 - Added user message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:09:46,588 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:10:18,736 - src.agent - INFO - models.py:165 - Response generated in 32.15s, length: 601 chars
2025-07-10 01:10:18,739 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 01:10:18,742 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 01:10:18,745 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:10:18,747 - src.agent - INFO - agent.py:67 - Generating python code for: What is my name?
2025-07-10 01:10:18,750 - src.agent - INFO - conversation.py:205 - Added user message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:10:18,752 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:11:29,970 - src.agent - INFO - models.py:165 - Response generated in 71.22s, length: 702 chars
2025-07-10 01:11:29,973 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 01:11:29,977 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 01:11:29,981 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:11:30,389 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:11:30,393 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:11:30,396 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:11:30,399 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 82c80986)
2025-07-10 01:11:30,403 - src.agent - INFO - agent.py:67 - Generating python code for: 
2025-07-10 01:11:30,405 - src.agent - ERROR - agent.py:122 - Code generation failed: Task description cannot be empty
2025-07-10 01:11:30,408 - src.agent - INFO - agent.py:67 - Generating invalid_language code for: create a function
2025-07-10 01:11:30,410 - src.agent - INFO - conversation.py:176 - Created new conversation: d9b75bf7-52e7-4c32-b5c6-9c530015a6e4 - New Conversation
2025-07-10 01:11:30,413 - src.agent - INFO - conversation.py:205 - Added user message to conversation d9b75bf7-52e7-4c32-b5c6-9c530015a6e4
2025-07-10 01:11:30,415 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 01:14:30,511 - src.agent - INFO - models.py:165 - Response generated in 180.09s, length: 2897 chars
2025-07-10 01:14:30,516 - src.agent - INFO - agent.py:174 - Validation not supported for invalid_language, returning code as-is
2025-07-10 01:14:30,519 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d9b75bf7-52e7-4c32-b5c6-9c530015a6e4
2025-07-10 01:14:31,017 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:14:31,024 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:14:31,029 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:14:31,031 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 4dbb8c84)
2025-07-10 01:14:31,035 - src.agent - INFO - conversation.py:176 - Created new conversation: 4377fe25-d086-4f90-aec5-77f221680ae7 - Test
2025-07-10 01:14:31,039 - src.agent - INFO - conversation.py:205 - Added user message to conversation 4377fe25-d086-4f90-aec5-77f221680ae7
2025-07-10 01:14:31,045 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:14:31,048 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:14:31,513 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:14:31,520 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:14:31,526 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:14:31,528 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: f386e5ff)
2025-07-10 01:14:31,532 - src.agent - INFO - agent.py:67 - Generating python code for: create a simple function
2025-07-10 01:14:31,534 - src.agent - INFO - conversation.py:176 - Created new conversation: b27a7722-0d5b-4a11-91df-6678ccb4e799 - New Conversation
2025-07-10 01:14:31,537 - src.agent - INFO - conversation.py:205 - Added user message to conversation b27a7722-0d5b-4a11-91df-6678ccb4e799
2025-07-10 01:14:31,544 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:16:02,384 - src.agent - INFO - models.py:165 - Response generated in 90.83s, length: 1505 chars
2025-07-10 01:16:02,388 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 22) (<unknown>, line 22)
2025-07-10 01:16:02,395 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 22) (<unknown>, line 22)']
2025-07-10 01:16:02,402 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation b27a7722-0d5b-4a11-91df-6678ccb4e799
2025-07-10 01:16:03,079 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:03,084 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:16:03,087 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:16:03,090 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: a738ddcc)
2025-07-10 01:16:03,093 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,095 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:16:03,532 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:03,536 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:16:03,540 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:16:03,542 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 784c796b)
2025-07-10 01:16:03,546 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,549 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,553 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,558 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,561 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,564 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,567 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,569 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,992 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:03,996 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:16:03,999 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:16:04,002 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: e7fd9ac5)
2025-07-10 01:16:04,011 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,015 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 01:16:04,020 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,026 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:16:04,029 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,032 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 01:16:04,035 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,037 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:16:04,466 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:04,888 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:05,311 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:05,730 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:07,753 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 01:16:07,760 - src.agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 01:16:08,208 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:10,228 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 01:16:10,231 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 01:16:12,242 - src.agent - INFO - models.py:165 - Response generated in 2.01s, length: 6 chars
2025-07-10 01:16:12,671 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:12,674 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 01:16:14,710 - src.agent - INFO - models.py:165 - Response generated in 2.03s, length: 0 chars
2025-07-10 08:30:07,028 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:07,058 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:07,061 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:30:07,065 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:07,069 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 7bbdd92c)
2025-07-10 08:30:07,545 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:07,551 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:07,554 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:07,557 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 13ce20df)
2025-07-10 08:30:07,560 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:07,563 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:08,109 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:08,116 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,123 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:08,130 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: b8272267)
2025-07-10 08:30:08,582 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:08,589 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,593 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:08,598 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 1b20a52d)
2025-07-10 08:30:08,661 - src.agent - INFO - conversation.py:176 - Created new conversation: 57e5c285-7ff5-4913-8a66-c4a05c4fd06c - Test Conversation
2025-07-10 08:30:08,676 - src.agent - INFO - conversation.py:176 - Created new conversation: 48f3ae18-4976-4e8a-8526-a4cb7a41516f - First
2025-07-10 08:30:08,681 - src.agent - INFO - conversation.py:176 - Created new conversation: 9398dc03-19db-492f-bf0b-9b1c027ad262 - Second
2025-07-10 08:30:08,689 - src.agent - INFO - conversation.py:193 - Switched to conversation: 48f3ae18-4976-4e8a-8526-a4cb7a41516f
2025-07-10 08:30:08,699 - src.agent - INFO - conversation.py:176 - Created new conversation: a6aa36e0-8d4e-4788-a8aa-ebbbd0f4461c - New Conversation
2025-07-10 08:30:08,706 - src.agent - INFO - conversation.py:205 - Added user message to conversation a6aa36e0-8d4e-4788-a8aa-ebbbd0f4461c
2025-07-10 08:30:08,716 - src.agent - INFO - conversation.py:176 - Created new conversation: 8fa56dd4-f5be-4dc5-a4eb-ec4b2a2c20d9 - First
2025-07-10 08:30:08,724 - src.agent - INFO - conversation.py:176 - Created new conversation: 630fe727-f54a-471d-ab47-9ca06ebc9023 - Second
2025-07-10 08:30:08,730 - src.agent - INFO - conversation.py:193 - Switched to conversation: 8fa56dd4-f5be-4dc5-a4eb-ec4b2a2c20d9
2025-07-10 08:30:08,739 - src.agent - INFO - conversation.py:205 - Added user message to conversation 8fa56dd4-f5be-4dc5-a4eb-ec4b2a2c20d9
2025-07-10 08:30:08,746 - src.agent - INFO - conversation.py:176 - Created new conversation: b90f42ee-b182-4ac3-b27b-3ba9c0434efa - To Delete
2025-07-10 08:30:08,748 - src.agent - INFO - conversation.py:233 - Deleted conversation: b90f42ee-b182-4ac3-b27b-3ba9c0434efa
2025-07-10 08:30:08,852 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,927 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,941 - src.agent - INFO - database.py:162 - Created conversation: c87ce201-63ae-4667-bbb7-91667adc80a4 - Test Conversation
2025-07-10 08:30:08,961 - src.agent - INFO - database.py:225 - Deleted conversation: c87ce201-63ae-4667-bbb7-91667adc80a4
2025-07-10 08:30:09,027 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,040 - src.agent - INFO - database.py:162 - Created conversation: bf85a0f8-635a-49a1-abe1-d42042e3e81c - Test Conversation
2025-07-10 08:30:09,050 - src.agent - DEBUG - database.py:269 - Added user message to conversation bf85a0f8-635a-49a1-abe1-d42042e3e81c
2025-07-10 08:30:09,060 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation bf85a0f8-635a-49a1-abe1-d42042e3e81c
2025-07-10 08:30:09,133 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,150 - src.agent - INFO - database.py:162 - Created conversation: 94938069-fe78-4cb2-a5be-2c181da9f100 - Test Conversation
2025-07-10 08:30:09,164 - src.agent - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 08:30:09,229 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,243 - src.agent - INFO - database.py:162 - Created conversation: 3ab5143b-daee-455c-85b4-f95ecb55d670 - Test Conversation
2025-07-10 08:30:09,256 - src.agent - INFO - database.py:397 - Saved project: Test Project
2025-07-10 08:30:09,318 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,389 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,850 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:09,854 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,857 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:09,859 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 974421ff)
2025-07-10 08:30:10,312 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:10,317 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:10,320 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:10,322 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: cccc828a)
2025-07-10 08:30:10,326 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:10,329 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:10,842 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:10,846 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:10,848 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:10,851 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 08ee945c)
2025-07-10 08:30:10,857 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:10,860 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:11,363 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:11,369 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:11,372 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:11,375 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 343879d5)
2025-07-10 08:30:11,379 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:11,382 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:11,890 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:11,895 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:11,898 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:11,900 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: efb433b6)
2025-07-10 08:30:11,905 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:30:11,908 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:30:11,911 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 08:30:11,915 - src.agent - INFO - validators.py:98 - HTML validation completed
2025-07-10 08:30:12,367 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:12,373 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:12,376 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:12,379 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 46b962e4)
2025-07-10 08:30:12,384 - src.agent - INFO - security.py:322 - SECURITY: code_generation_request - {'task_length': 16, 'language': 'python', 'session_id': '46b962e4-6c49-4c64-a73b-a6d14d5a863c'}
2025-07-10 08:30:12,388 - src.agent - INFO - agent.py:88 - Generating python code for: My name is Alice
2025-07-10 08:30:12,391 - src.agent - INFO - conversation.py:176 - Created new conversation: 31870cc0-c306-4159-af26-05a6112ee225 - New Conversation
2025-07-10 08:30:12,394 - src.agent - INFO - conversation.py:205 - Added user message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:30:12,400 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: backend
2025-07-10 08:30:49,634 - src.agent - INFO - models.py:198 - Response generated in 37.23s, length: 312 chars
2025-07-10 08:30:49,680 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 08:30:49,684 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 08:30:49,727 - src.agent - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 08:30:49,731 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:30:49,739 - src.agent - INFO - security.py:322 - SECURITY: code_generation_request - {'task_length': 16, 'language': 'python', 'session_id': '46b962e4-6c49-4c64-a73b-a6d14d5a863c'}
2025-07-10 08:30:49,745 - src.agent - INFO - agent.py:88 - Generating python code for: What is my name?
2025-07-10 08:30:49,757 - src.agent - INFO - conversation.py:205 - Added user message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:30:49,764 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: backend
2025-07-10 08:31:47,178 - src.agent - INFO - models.py:198 - Response generated in 57.41s, length: 558 chars
2025-07-10 08:31:47,231 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 08:31:47,236 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 08:31:47,276 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:31:47,813 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:47,822 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:47,828 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:47,833 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 6587ad2b)
2025-07-10 08:31:47,841 - src.agent - INFO - security.py:322 - SECURITY: code_generation_request - {'task_length': 0, 'language': 'python', 'session_id': '6587ad2b-8429-4425-8dca-b8e6b7c6658a'}
2025-07-10 08:31:47,845 - src.agent - INFO - agent.py:88 - Generating python code for: 
2025-07-10 08:31:47,848 - src.agent - ERROR - agent.py:154 - Code generation failed: Task description cannot be empty
2025-07-10 08:31:47,851 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:31:47,858 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:31:48,389 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:48,393 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:48,396 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:48,398 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 2a77f319)
2025-07-10 08:31:48,403 - src.agent - INFO - conversation.py:176 - Created new conversation: bc999670-04a0-46e9-8278-34d7f60d4243 - Test
2025-07-10 08:31:48,406 - src.agent - INFO - conversation.py:205 - Added user message to conversation bc999670-04a0-46e9-8278-34d7f60d4243
2025-07-10 08:31:48,410 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:48,413 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:48,832 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:48,837 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:48,840 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:48,843 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 66eadcb5)
2025-07-10 08:31:48,847 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:31:48,850 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:31:49,330 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:49,334 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:49,339 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:49,343 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 2f7cf745)
2025-07-10 08:31:49,349 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,353 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:49,774 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:49,778 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:49,781 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:49,783 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d047ce3f)
2025-07-10 08:31:49,789 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,791 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:49,794 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,796 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:49,799 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,802 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:49,805 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,808 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:50,237 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:50,240 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:50,243 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:50,245 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: badbfc45)
2025-07-10 08:31:50,249 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,252 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 08:31:50,255 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,258 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:50,261 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,264 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 08:31:50,267 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,271 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:50,705 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:51,131 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:51,562 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:51,983 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:54,058 - src.agent - INFO - models.py:99 - Ollama connection successful. Available models: 4
2025-07-10 08:31:54,066 - src.agent - INFO - models.py:122 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 08:31:54,547 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:56,573 - src.agent - INFO - models.py:99 - Ollama connection successful. Available models: 4
2025-07-10 08:31:56,576 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: assistant
2025-07-10 08:32:18,136 - src.agent - INFO - models.py:198 - Response generated in 21.56s, length: 398 chars
2025-07-10 08:32:18,607 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:32:18,610 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: assistant
2025-07-10 08:32:20,651 - src.agent - INFO - models.py:198 - Response generated in 2.04s, length: 0 chars
2025-07-10 08:32:20,753 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:20,757 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:20,826 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:20,830 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:21,020 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:21,023 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:21,105 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:21,108 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:21,328 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:21,331 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:24,259 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:24,262 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:26,365 - src.agent - WARNING - monitoring.py:327 - Circuit breaker opened after 3 failures
2025-07-10 08:32:26,375 - src.agent - WARNING - monitoring.py:327 - Circuit breaker opened after 3 failures
2025-07-10 08:32:27,479 - src.agent - INFO - monitoring.py:297 - Circuit breaker attempting reset
2025-07-10 08:32:27,563 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,567 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,702 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,772 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,776 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,842 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,846 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,850 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,857 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,925 - src.agent - WARNING - monitoring.py:154 - Performance threshold exceeded: test_operation took 62.23ms (threshold: 50ms)
2025-07-10 08:32:27,932 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:32:27,982 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: <script[^>]*>.*?</script>
2025-07-10 08:32:28,095 - src.agent - WARNING - security.py:270 - Rate limit exceeded for client: test_client
2025-07-10 08:32:28,109 - src.agent - WARNING - security.py:270 - Rate limit exceeded for client: client_1
2025-07-10 08:32:28,113 - src.agent - WARNING - security.py:270 - Rate limit exceeded for client: client_2
2025-07-10 08:32:28,122 - src.agent - INFO - security.py:322 - SECURITY: test_event - {'detail': 'test_detail'}
2025-07-10 08:32:28,126 - src.agent - CRITICAL - security.py:316 - SECURITY: critical_event - {'detail': 'critical_detail'}
2025-07-10 08:32:28,156 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (eval|exec|system|popen|subprocess)\s*\(
2025-07-10 08:32:28,161 - src.agent - CRITICAL - security.py:316 - SECURITY: dangerous_code_detected - {'code': "import os; os.system('rm -rf /')", 'issues': ['Blocked module import: os']}
2025-07-10 08:55:25,045 - src.security - WARNING - security.py:81 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 08:55:25,053 - src.security - WARNING - security.py:81 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 08:56:10,439 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 08:56:10,445 - src.security - WARNING - security.py:81 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 08:56:10,447 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (eval|exec)\s*\(
2025-07-10 08:56:10,449 - src.security - WARNING - security.py:81 - Dangerous pattern detected: \.\./+(etc|windows|system32)
