# AI Coding Agent - Code Validation
"""
Code validation and security checking for generated code
"""

import ast
import tempfile
import subprocess
import re
from pathlib import Path
from typing import Tuple, List, Dict, Any

from .logger import get_logger
from .exceptions import CodeValidationError, SecurityError

class CodeValidator:
    """
    Validates generated code for syntax, security, and best practices
    """
    
    def __init__(self):
        """Initialize code validator"""
        self.temp_dir = Path(tempfile.gettempdir()) / "coding_agent_validation"
        self.temp_dir.mkdir(exist_ok=True)
        self.logger = get_logger(__name__)

        # Security patterns to detect dangerous code
        self.dangerous_patterns = [
            r'import\s+os',
            r'import\s+subprocess',
            r'import\s+sys',
            r'eval\s*\(',
            r'exec\s*\(',
            r'__import__\s*\(',
            r'open\s*\(',
            r'file\s*\(',
            r'input\s*\(',
            r'raw_input\s*\(',
        ]
        
    def validate_python(self, code: str) -> Tuple[bool, List[str]]:
        """
        Comprehensive Python code validation for syntax, security, and best practices

        Args:
            code: Python code to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        warnings = []

        # 1. Syntax validation
        try:
            parsed = ast.parse(code)
            self.logger.debug("Python syntax validation passed")
        except SyntaxError as e:
            error_msg = f"Syntax Error: {e}"
            errors.append(error_msg)
            self.logger.warning(f"Syntax validation failed: {error_msg}")
            return False, errors

        # 2. Security validation
        security_errors = self._check_security_patterns(code)
        errors.extend(security_errors)

        # 3. Best practices check
        best_practice_warnings = self._check_best_practices(code)
        warnings.extend(best_practice_warnings)

        is_valid = len(errors) == 0
        all_issues = errors + warnings

        if is_valid:
            self.logger.info(f"Python code validation passed with {len(warnings)} warnings")
        else:
            self.logger.warning(f"Python code validation failed with {len(errors)} errors")

        return is_valid, all_issues
    
    def validate_html(self, html_code: str) -> Tuple[bool, List[str]]:
        """
        Basic HTML validation
        
        Args:
            html_code: HTML code to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Basic HTML structure check
        if not html_code.strip().startswith('<'):
            errors.append("HTML should start with a tag")
            
        self.logger.info("HTML validation completed")
        return len(errors) == 0, errors

    def _check_security_patterns(self, code: str) -> List[str]:
        """Check for dangerous patterns using regex"""
        import re
        errors = []

        for pattern in self.dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                errors.append(f"Security risk: Detected dangerous pattern '{pattern}'")

        return errors

    def _check_best_practices(self, code: str) -> List[str]:
        """Check for Python best practices"""
        warnings = []
        lines = code.split('\n')

        for i, line in enumerate(lines, 1):
            # Check line length
            if len(line) > 120:
                warnings.append(f"Line {i}: Line too long ({len(line)} > 120 characters)")

            # Check for print statements (should use logging)
            if 'print(' in line and not line.strip().startswith('#'):
                warnings.append(f"Line {i}: Consider using logging instead of print()")

        return warnings

    def validate_javascript(self, code: str) -> Tuple[bool, List[str]]:
        """
        Basic JavaScript validation

        Args:
            code: JavaScript code to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        warnings = []

        # Basic security checks
        dangerous_js_patterns = [
            r'eval\s*\(',
            r'document\.write\s*\(',
            r'innerHTML\s*=',
            r'outerHTML\s*=',
        ]

        import re
        for pattern in dangerous_js_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                errors.append(f"Security risk: Detected dangerous JavaScript pattern '{pattern}'")

        # Basic syntax checks
        if code.count('(') != code.count(')'):
            errors.append("Mismatched parentheses")
        if code.count('{') != code.count('}'):
            errors.append("Mismatched braces")
        if code.count('[') != code.count(']'):
            errors.append("Mismatched brackets")

        is_valid = len(errors) == 0
        self.logger.info(f"JavaScript validation completed: {'passed' if is_valid else 'failed'}")
        return is_valid, errors + warnings
