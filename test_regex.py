import re

filename = "../../../etc/passwd"
print(f"Input: {repr(filename)}")

# Test the regex pattern
pattern1 = r'[<>:"/\\|?*\x00-\x1f]'
sanitized = re.sub(pattern1, '_', filename)
print(f"After first pattern: {repr(sanitized)}")

# Handle path traversal patterns specifically
sanitized = re.sub(r'\.\.', '_.', sanitized)
print(f"Output: {repr(sanitized)}")
print(f"Expected: {repr('_.._.._.._etc_passwd')}")
print(f"Match: {sanitized == '_.._.._.._etc_passwd'}")
