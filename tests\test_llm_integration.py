# AI Coding Agent - LLM Integration Tests
"""
Unit tests for LLM integration and model management
"""

import pytest
import sys
import os

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.models import LLMManager, ModelConfig
from src.exceptions import LLMConnectionError, LLMResponseError

class TestLLMManager:
    """Test cases for LLM Manager functionality"""
    
    def test_llm_manager_initialization(self):
        """Test that LLM manager initializes correctly"""
        try:
            manager = LLMManager()
            assert manager is not None
            assert manager.models is not None
            assert len(manager.models) > 0
            
            # Check that all expected roles are configured
            expected_roles = ['frontend', 'backend', 'debugger', 'assistant']
            for role in expected_roles:
                assert role in manager.models
                assert isinstance(manager.models[role], ModelConfig)
                
        except LLMConnectionError:
            # If Ollama is not running, skip this test
            pytest.skip("Ollama service not available")
    
    def test_model_configurations(self):
        """Test model configurations are valid"""
        try:
            manager = LLMManager()
            
            for role, config in manager.models.items():
                assert config.name is not None
                assert config.specialty is not None
                assert isinstance(config.temperature, float)
                assert isinstance(config.max_tokens, int)
                assert isinstance(config.context_window, int)
                assert 0.0 <= config.temperature <= 1.0
                assert config.max_tokens > 0
                assert config.context_window > 0
                
        except LLMConnectionError:
            pytest.skip("Ollama service not available")
    
    def test_get_model_config(self):
        """Test getting model configuration by role"""
        try:
            manager = LLMManager()
            
            # Test valid role
            config = manager.get_model('assistant')
            assert config is not None
            assert isinstance(config, ModelConfig)
            
            # Test invalid role
            config = manager.get_model('nonexistent')
            assert config is None
            
        except LLMConnectionError:
            pytest.skip("Ollama service not available")
    
    @pytest.mark.slow
    def test_ollama_connection(self):
        """Test connection to Ollama service (marked as slow)"""
        try:
            manager = LLMManager()
            
            # Test connection
            is_connected = manager.test_connection()
            
            if is_connected:
                # If connected, test getting available models
                models = manager.get_available_models()
                assert isinstance(models, list)
                # Should have at least phi3:mini if properly set up
                print(f"Available models: {models}")
            else:
                pytest.skip("Ollama service not running or not accessible")
                
        except LLMConnectionError:
            pytest.skip("Ollama service not available")
    
    @pytest.mark.slow
    def test_generate_response_basic(self):
        """Test basic response generation (marked as slow)"""
        try:
            manager = LLMManager()
            
            # Test connection first
            if not manager.test_connection():
                pytest.skip("Ollama service not running")
            
            # Test simple response generation
            prompt = "Say hello in one word"
            response = manager.generate_response(prompt, "assistant")
            
            assert isinstance(response, str)
            assert len(response) > 0
            print(f"Generated response: {response}")
            
        except (LLMConnectionError, LLMResponseError):
            pytest.skip("Ollama service not available or model not found")
    
    def test_error_handling(self):
        """Test error handling for invalid inputs"""
        try:
            manager = LLMManager()
            
            # Test with empty prompt (should still work but return something)
            response = manager.generate_response("", "assistant")
            assert isinstance(response, str)
            
        except LLMConnectionError:
            pytest.skip("Ollama service not available")
        except LLMResponseError:
            # This is expected for empty prompts in some cases
            pass

class TestModelConfig:
    """Test cases for ModelConfig dataclass"""
    
    def test_model_config_creation(self):
        """Test ModelConfig creation"""
        config = ModelConfig(
            name="test-model",
            specialty="Testing",
            temperature=0.5,
            max_tokens=1000,
            context_window=2000
        )
        
        assert config.name == "test-model"
        assert config.specialty == "Testing"
        assert config.temperature == 0.5
        assert config.max_tokens == 1000
        assert config.context_window == 2000
