"""
Integration tests for the AI Coding Agent
Tests the interaction between all major components
"""

import pytest
import sys
import os
import tempfile
import uuid
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.agent import CodingAgent
from src.models import LLMManager
from src.conversation import ConversationManager
from src.database import DatabaseManager
from src.validators import CodeValidator
from src.config import get_config

class TestIntegration:
    """Integration tests for the complete system"""
    
    def setup_method(self):
        """Set up test environment"""
        self.agent = CodingAgent()
    
    def test_agent_initialization_integration(self):
        """Test that all components initialize correctly together"""
        assert self.agent is not None
        assert self.agent.session_id is not None
        assert len(self.agent.session_id) > 0
        
        # Check that all components are initialized
        assert hasattr(self.agent, 'llm_manager')
        assert hasattr(self.agent, 'conversation_manager')
        assert hasattr(self.agent, 'validator')
        assert hasattr(self.agent, 'database')
    
    def test_code_generation_with_validation_integration(self):
        """Test end-to-end code generation with validation"""
        task = "create a simple hello world function"
        language = "python"
        
        # Generate code
        result = self.agent.generate_code(task, language)
        
        # Verify result
        assert result is not None
        assert len(result) > 0
        assert isinstance(result, str)
        
        # Check that validation was applied (should not have validation errors in comments)
        # If validation failed, errors would be added as comments
        lines = result.split('\n')
        validation_error_lines = [line for line in lines if line.startswith('# VALIDATION ERROR:')]
        
        # For a simple hello world function, there should be no validation errors
        if validation_error_lines:
            pytest.skip(f"Validation errors found (expected for complex code): {validation_error_lines}")
    
    def test_conversation_persistence_integration(self):
        """Test that conversations are properly managed and persisted"""
        # Generate some code to create conversation history
        task1 = "create a function that adds two numbers"
        task2 = "now create a function that multiplies two numbers"
        
        result1 = self.agent.generate_code(task1, "python")
        result2 = self.agent.generate_code(task2, "python")
        
        # Check conversation manager has the messages
        current_conv = self.agent.conversation_manager.get_current_conversation()
        assert current_conv is not None
        assert len(current_conv.messages) >= 4  # 2 user messages + 2 assistant responses
        
        # Check message content
        user_messages = [msg for msg in current_conv.messages if msg.role == "user"]
        assert len(user_messages) >= 2
        assert task1 in user_messages[0].content
        assert task2 in user_messages[1].content
    
    def test_database_integration(self):
        """Test database integration with the agent"""
        if self.agent.database is None:
            pytest.skip("Database not available in test environment")
        
        # Create a conversation through the agent
        task = "create a test function"
        result = self.agent.generate_code(task, "python")
        
        # Check if conversation was saved to database
        current_conv = self.agent.conversation_manager.get_current_conversation()
        if current_conv:
            # Try to save conversation to database
            try:
                self.agent.database.create_conversation(
                    current_conv.id, 
                    current_conv.title
                )
                
                # Add messages to database
                for msg in current_conv.messages:
                    self.agent.database.add_message(
                        msg.id, current_conv.id, msg.role, msg.content
                    )
                
                # Verify data was saved
                saved_conv = self.agent.database.get_conversation(current_conv.id)
                assert saved_conv is not None
                assert saved_conv['title'] == current_conv.title
                
            except Exception as e:
                pytest.skip(f"Database integration test failed: {e}")
    
    def test_validator_integration(self):
        """Test validator integration with different code types"""
        # Test Python validation
        python_code = "def hello():\n    print('Hello, World!')\n    return 'hello'"
        is_valid, issues = self.agent.validator.validate_python(python_code)
        assert isinstance(is_valid, bool)
        assert isinstance(issues, list)
        
        # Test JavaScript validation
        js_code = "function hello() { return 'Hello, World!'; }"
        is_valid_js, issues_js = self.agent.validator.validate_javascript(js_code)
        assert isinstance(is_valid_js, bool)
        assert isinstance(issues_js, list)
        
        # Test HTML validation
        html_code = "<html><body><h1>Hello World</h1></body></html>"
        is_valid_html, issues_html = self.agent.validator.validate_html(html_code)
        assert isinstance(is_valid_html, bool)
        assert isinstance(issues_html, list)
    
    def test_llm_conversation_context_integration(self):
        """Test that LLM uses conversation context properly"""
        if self.agent.llm_manager is None:
            pytest.skip("LLM not available in test environment")
        
        # First request
        task1 = "My name is Alice"
        result1 = self.agent.generate_code(task1, "python", use_conversation_context=True)
        
        # Second request that should use context
        task2 = "What is my name?"
        result2 = self.agent.generate_code(task2, "python", use_conversation_context=True)
        
        # The second response should somehow reference the first request
        # This is a basic check - in a real scenario, the LLM should remember "Alice"
        assert result1 is not None
        assert result2 is not None
        assert len(result1) > 0
        assert len(result2) > 0
    
    def test_error_handling_integration(self):
        """Test error handling across components"""
        # Test with empty task
        try:
            result = self.agent.generate_code("", "python")
            # Should either raise an error or return a meaningful response
            assert result is not None
        except Exception as e:
            # Error handling is working
            assert "empty" in str(e).lower() or "invalid" in str(e).lower()
        
        # Test with invalid language
        result = self.agent.generate_code("create a function", "invalid_language")
        assert result is not None  # Should handle gracefully
    
    def test_component_isolation(self):
        """Test that components can work independently if others fail"""
        # Test conversation manager without LLM
        conv_manager = ConversationManager()
        conv = conv_manager.create_conversation("Test")
        conv_manager.add_message_to_current("user", "test message")
        
        messages = conv.messages
        assert len(messages) == 1
        assert messages[0].content == "test message"
        
        # Test validator independently
        validator = CodeValidator()
        is_valid, issues = validator.validate_python("print('hello')")
        assert isinstance(is_valid, bool)
        assert isinstance(issues, list)

class TestPerformanceIntegration:
    """Performance integration tests"""
    
    def setup_method(self):
        """Set up performance test environment"""
        self.agent = CodingAgent()
    
    def test_code_generation_performance(self):
        """Test code generation performance"""
        import time
        
        task = "create a simple function"
        start_time = time.time()
        
        result = self.agent.generate_code(task, "python")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within reasonable time (120 seconds for LLM call including model loading)
        assert duration < 120.0, f"Code generation took too long: {duration:.2f}s"
        assert result is not None
    
    def test_validation_performance(self):
        """Test validation performance"""
        import time
        
        # Test with moderately complex code
        code = """
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    for i in range(10):
        print(f"fibonacci({i}) = {fibonacci(i)}")

if __name__ == "__main__":
    main()
"""
        
        start_time = time.time()
        is_valid, issues = self.agent.validator.validate_python(code)
        end_time = time.time()
        
        duration = end_time - start_time
        
        # Validation should be fast (under 1 second)
        assert duration < 1.0, f"Validation took too long: {duration:.2f}s"
        assert isinstance(is_valid, bool)
        assert isinstance(issues, list)

class TestSecurityIntegration:
    """Security integration tests"""
    
    def setup_method(self):
        """Set up security test environment"""
        self.agent = CodingAgent()
    
    def test_dangerous_code_detection(self):
        """Test that dangerous code patterns are detected"""
        dangerous_codes = [
            "import os; os.system('rm -rf /')",
            "eval(user_input)",
            "exec(malicious_code)",
            "__import__('subprocess').call(['rm', '-rf', '/'])"
        ]
        
        for dangerous_code in dangerous_codes:
            is_valid, issues = self.agent.validator.validate_python(dangerous_code)
            
            # Should detect security issues
            security_issues = [issue for issue in issues if 'security' in issue.lower() or 'dangerous' in issue.lower()]
            assert len(security_issues) > 0, f"Failed to detect security issue in: {dangerous_code}"
    
    def test_safe_code_acceptance(self):
        """Test that safe code is accepted"""
        safe_codes = [
            "def add(a, b): return a + b",
            "print('Hello, World!')",
            "x = [1, 2, 3, 4, 5]",
            "for i in range(10): print(i)"
        ]
        
        for safe_code in safe_codes:
            is_valid, issues = self.agent.validator.validate_python(safe_code)
            
            # Should not have security errors (warnings are OK)
            security_errors = [issue for issue in issues if 'security risk' in issue.lower()]
            assert len(security_errors) == 0, f"False positive security detection in: {safe_code}"
