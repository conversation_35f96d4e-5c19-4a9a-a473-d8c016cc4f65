from src.security import InputSanitizer
from src.exceptions import SecurityError

sanitizer = InputSanitizer()

dangerous_inputs = [
    "rm -rf /",
    "SELECT * FROM users; DROP TABLE users;--",
    "eval(malicious_code)",
    "../../../etc/passwd",
    "subprocess.call(['rm', '-rf', '/'])"
]

for dangerous_input in dangerous_inputs:
    try:
        result = sanitizer.sanitize_text_input(dangerous_input)
        print(f"PASSED (should have failed): {repr(dangerous_input)} -> {repr(result)}")
    except SecurityError as e:
        print(f"FAILED (correctly): {repr(dangerous_input)} -> {e}")
    except Exception as e:
        print(f"ERROR: {repr(dangerous_input)} -> {e}")
