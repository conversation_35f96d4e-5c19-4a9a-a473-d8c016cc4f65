# AI Coding Agent - Environment Variables Template
# Copy this file to .env and fill in your values

# Development Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Flask Configuration
FLASK_APP=src/app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_TIMEOUT=30

# Database Configuration
DATABASE_URL=sqlite:///database/coding_agent.db

# API Configuration
API_HOST=localhost
API_PORT=5000

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:5000

# Logging
LOG_FILE=logs/coding_agent.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
