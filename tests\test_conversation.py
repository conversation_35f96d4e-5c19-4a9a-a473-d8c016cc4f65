# AI Coding Agent - Conversation Tests
"""
Unit tests for conversation context management
"""

import pytest
import sys
import os
from datetime import datetime

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.conversation import Message, Conversation, ConversationManager

class TestMessage:
    """Test cases for Message class"""
    
    def test_message_creation(self):
        """Test message creation with defaults"""
        message = Message(role="user", content="Hello world")
        
        assert message.role == "user"
        assert message.content == "Hello world"
        assert message.id is not None
        assert len(message.id) > 0
        assert isinstance(message.timestamp, datetime)
        assert isinstance(message.metadata, dict)
    
    def test_message_to_dict(self):
        """Test message serialization"""
        message = Message(role="assistant", content="Hi there", metadata={"test": "value"})
        data = message.to_dict()
        
        assert data["role"] == "assistant"
        assert data["content"] == "Hi there"
        assert data["metadata"]["test"] == "value"
        assert "id" in data
        assert "timestamp" in data
    
    def test_message_from_dict(self):
        """Test message deserialization"""
        data = {
            "id": "test-id",
            "role": "user",
            "content": "Test message",
            "timestamp": "2025-01-01T12:00:00",
            "metadata": {"key": "value"}
        }
        
        message = Message.from_dict(data)
        assert message.id == "test-id"
        assert message.role == "user"
        assert message.content == "Test message"
        assert message.metadata["key"] == "value"

class TestConversation:
    """Test cases for Conversation class"""
    
    def test_conversation_creation(self):
        """Test conversation creation"""
        conv = Conversation(title="Test Conversation")
        
        assert conv.title == "Test Conversation"
        assert conv.id is not None
        assert len(conv.messages) == 0
        assert isinstance(conv.created_at, datetime)
        assert isinstance(conv.updated_at, datetime)
    
    def test_add_message(self):
        """Test adding messages to conversation"""
        conv = Conversation()
        
        # Add first message
        msg1 = conv.add_message("user", "Hello")
        assert len(conv.messages) == 1
        assert msg1.role == "user"
        assert msg1.content == "Hello"
        
        # Add second message
        msg2 = conv.add_message("assistant", "Hi there!")
        assert len(conv.messages) == 2
        assert msg2.role == "assistant"
        assert msg2.content == "Hi there!"
    
    def test_auto_title_generation(self):
        """Test automatic title generation from first user message"""
        conv = Conversation()
        
        # Add first user message
        conv.add_message("user", "How do I create a Python function?")
        assert conv.title == "How do I create a Python function?"
        
        # Long message should be truncated
        conv2 = Conversation()
        long_message = "This is a very long message that should be truncated when used as a title because it exceeds the maximum length"
        conv2.add_message("user", long_message)
        assert len(conv2.title) <= 53  # 50 chars + "..."
        assert conv2.title.endswith("...")
    
    def test_get_context_messages(self):
        """Test getting context messages with limits"""
        conv = Conversation()
        
        # Add several messages
        for i in range(10):
            conv.add_message("user", f"Message {i}")
            conv.add_message("assistant", f"Response {i}")
        
        # Test max_messages limit
        context = conv.get_context_messages(max_messages=5)
        assert len(context) == 5
        
        # Test without limit
        all_context = conv.get_context_messages()
        assert len(all_context) == 20  # 10 user + 10 assistant
    
    def test_get_formatted_context(self):
        """Test formatted context string generation"""
        conv = Conversation()
        
        conv.add_message("user", "Hello")
        conv.add_message("assistant", "Hi there!")
        conv.add_message("user", "How are you?")
        
        formatted = conv.get_formatted_context()
        
        assert "Human: Hello" in formatted
        assert "Assistant: Hi there!" in formatted
        assert "Human: How are you?" in formatted
        assert formatted.count("\n\n") == 2  # Two separators for three messages
    
    def test_conversation_serialization(self):
        """Test conversation to/from dict"""
        conv = Conversation(title="Test")
        conv.add_message("user", "Hello")
        conv.add_message("assistant", "Hi!")
        
        # Serialize
        data = conv.to_dict()
        assert data["title"] == "Test"
        assert len(data["messages"]) == 2
        
        # Deserialize
        conv2 = Conversation.from_dict(data)
        assert conv2.title == "Test"
        assert len(conv2.messages) == 2
        assert conv2.messages[0].content == "Hello"
        assert conv2.messages[1].content == "Hi!"

class TestConversationManager:
    """Test cases for ConversationManager class"""
    
    def test_manager_creation(self):
        """Test conversation manager creation"""
        manager = ConversationManager()
        
        assert len(manager.conversations) == 0
        assert manager.current_conversation_id is None
    
    def test_create_conversation(self):
        """Test creating new conversations"""
        manager = ConversationManager()
        
        conv = manager.create_conversation("Test Conversation")
        
        assert conv.title == "Test Conversation"
        assert conv.id in manager.conversations
        assert manager.current_conversation_id == conv.id
    
    def test_conversation_management(self):
        """Test conversation management operations"""
        manager = ConversationManager()
        
        # Create conversations
        conv1 = manager.create_conversation("First")
        conv2 = manager.create_conversation("Second")
        
        # Test getting conversations
        assert manager.get_conversation(conv1.id) == conv1
        assert manager.get_conversation(conv2.id) == conv2
        assert manager.get_current_conversation() == conv2
        
        # Test switching conversations
        assert manager.set_current_conversation(conv1.id)
        assert manager.get_current_conversation() == conv1
        
        # Test invalid conversation ID
        assert not manager.set_current_conversation("invalid-id")
    
    def test_add_message_to_current(self):
        """Test adding messages to current conversation"""
        manager = ConversationManager()
        
        # Should create conversation if none exists
        msg = manager.add_message_to_current("user", "Hello")
        assert msg is not None
        assert msg.content == "Hello"
        
        current = manager.get_current_conversation()
        assert current is not None
        assert len(current.messages) == 1
    
    def test_conversation_list(self):
        """Test getting conversation list"""
        manager = ConversationManager()
        
        # Create some conversations
        conv1 = manager.create_conversation("First")
        conv2 = manager.create_conversation("Second")
        
        # Add messages to make them different
        manager.set_current_conversation(conv1.id)
        manager.add_message_to_current("user", "Hello")
        
        conv_list = manager.get_conversation_list()
        
        assert len(conv_list) == 2
        assert all("id" in conv for conv in conv_list)
        assert all("title" in conv for conv in conv_list)
        assert all("message_count" in conv for conv in conv_list)
    
    def test_delete_conversation(self):
        """Test deleting conversations"""
        manager = ConversationManager()
        
        conv = manager.create_conversation("To Delete")
        conv_id = conv.id
        
        # Delete conversation
        assert manager.delete_conversation(conv_id)
        assert conv_id not in manager.conversations
        assert manager.current_conversation_id is None
        
        # Try to delete non-existent conversation
        assert not manager.delete_conversation("invalid-id")
