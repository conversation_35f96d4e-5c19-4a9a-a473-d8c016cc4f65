# AI Coding Agent - Logging Tests
"""
Unit tests for logging and error handling
"""

import pytest
import sys
import os
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.logger import get_logger, CodingAgentLogger
from src.exceptions import CodingAgentError, LLMConnectionError
from src.config import get_config

class TestLogging:
    """Test cases for logging functionality"""
    
    def test_logger_creation(self):
        """Test that logger can be created"""
        logger = get_logger("test_logger")
        assert logger is not None
        # The logger name will be based on the module that calls it
        assert "agent" in logger.name
    
    def test_logger_instance(self):
        """Test CodingAgentLogger instance"""
        logger_instance = CodingAgentLogger("test")
        logger = logger_instance.get_logger()
        assert logger is not None
        assert logger.name == "test"
    
    def test_config_loading(self):
        """Test configuration loading"""
        config = get_config()
        assert config is not None
        assert hasattr(config, 'LOG_LEVEL')
        assert hasattr(config, 'ENVIRONMENT')

class TestExceptions:
    """Test cases for custom exceptions"""
    
    def test_base_exception(self):
        """Test base CodingAgentError"""
        error = CodingAgentError("Test error", "TEST_ERROR", {"detail": "test"})
        assert error.message == "Test error"
        assert error.error_code == "TEST_ERROR"
        assert error.details == {"detail": "test"}
        
        error_dict = error.to_dict()
        assert error_dict["error"] is True
        assert error_dict["error_code"] == "TEST_ERROR"
        assert error_dict["message"] == "Test error"
    
    def test_llm_connection_error(self):
        """Test LLM connection error"""
        error = LLMConnectionError("Connection failed")
        assert error.error_code == "LLM_CONNECTION_ERROR"
        assert "Connection failed" in error.message
    
    def test_exception_inheritance(self):
        """Test that custom exceptions inherit from base"""
        error = LLMConnectionError()
        assert isinstance(error, CodingAgentError)
        assert isinstance(error, Exception)
