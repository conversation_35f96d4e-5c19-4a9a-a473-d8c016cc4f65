# AI Coding Agent - LLM Model Management
"""
Management of multiple specialized LLM models for different coding tasks
"""

from typing import Optional, List
from dataclasses import dataclass
import time

from .logger import get_logger
from .exceptions import LLMConnectionError, LLMResponseError
from .config import get_config
from .monitoring import CircuitBreaker

config = get_config()

@dataclass
class ModelConfig:
    """Configuration for individual LLM models"""
    name: str
    specialty: str
    temperature: float
    max_tokens: int
    context_window: int

class LLMManager:
    """
    Manages multiple specialized LLM models for different coding tasks
    """
    
    def __init__(self):
        """Initialize LLM manager with model configurations"""
        self.logger = get_logger(__name__)

        # Circuit breaker for resilient LLM calls
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=60,
            expected_exception=LLMConnectionError
        )

        # Model configurations for different specialties
        self.models = {
            'frontend': ModelConfig(
                name="phi3:mini",
                specialty="HTML/CSS/JavaScript generation",
                temperature=0.3,
                max_tokens=2048,
                context_window=4096
            ),
            'backend': ModelConfig(
                name="phi3:mini",  # Using phi3:mini for all initially
                specialty="Python/Flask backend development",
                temperature=0.2,
                max_tokens=4096,
                context_window=4096
            ),
            'debugger': ModelConfig(
                name="phi3:mini",
                specialty="Error analysis and code fixing",
                temperature=0.1,
                max_tokens=2048,
                context_window=4096
            ),
            'assistant': ModelConfig(
                name="phi3:mini",
                specialty="General assistance and explanations",
                temperature=0.5,
                max_tokens=2048,
                context_window=4096
            )
        }

        self.llm_instances = {}
        self.ollama_client = None
        self._initialize_ollama()

    def _initialize_ollama(self):
        """Initialize Ollama client"""
        try:
            import ollama
            self.ollama_client = ollama.Client(host=config.OLLAMA_HOST)
            self.logger.info(f"Ollama client initialized with host: {config.OLLAMA_HOST}")
        except ImportError:
            self.logger.error("Ollama package not installed. Run: pip install ollama")
            raise LLMConnectionError("Ollama package not available")
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama client: {e}")
            raise LLMConnectionError(f"Failed to connect to Ollama: {e}")

    def test_connection(self) -> bool:
        """Test connection to Ollama service"""
        try:
            if not self.ollama_client:
                return False

            # Try to list available models
            models = self.ollama_client.list()
            self.logger.info(f"Ollama connection successful. Available models: {len(models.get('models', []))}")
            return True

        except Exception as e:
            self.logger.error(f"Ollama connection test failed: {e}")
            return False

    def get_available_models(self) -> List[str]:
        """Get list of available models from Ollama"""
        try:
            if not self.ollama_client:
                return []

            response = self.ollama_client.list()
            models = []
            for model in response.get('models', []):
                # Handle different response formats
                if isinstance(model, dict):
                    model_name = model.get('name') or model.get('model', 'unknown')
                else:
                    model_name = str(model)
                models.append(model_name)

            self.logger.info(f"Available models: {models}")
            return models

        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return []

    def generate_response(self, prompt: str, role: str = "assistant") -> str:
        """
        Generate response using specified model role with retry logic

        Args:
            prompt: Input prompt for the model
            role: Model role (frontend, backend, debugger, assistant)

        Returns:
            Generated response text

        Raises:
            LLMConnectionError: If connection to Ollama fails
            LLMResponseError: If model response is invalid
        """
        return self._generate_response_with_retry(prompt, role, max_retries=3)

    def _generate_response_with_retry(self, prompt: str, role: str, max_retries: int = 3) -> str:
        """Generate response with exponential backoff retry logic"""
        import time

        for attempt in range(max_retries + 1):
            try:
                return self.circuit_breaker.call(self._generate_response_internal, prompt, role)
            except LLMConnectionError as e:
                if attempt == max_retries:
                    raise e

                # Exponential backoff: 1s, 2s, 4s
                wait_time = 2 ** attempt
                self.logger.warning(f"LLM call failed (attempt {attempt + 1}/{max_retries + 1}), retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
            except Exception as e:
                # For non-connection errors, don't retry
                raise e

        raise LLMConnectionError("Max retries exceeded")

    def _generate_response_internal(self, prompt: str, role: str) -> str:
        """Internal method for generating response (used by retry logic)"""
        try:
            if not self.ollama_client:
                raise LLMConnectionError("Ollama client not initialized")

            if role not in self.models:
                role = "assistant"  # Default fallback

            model_config = self.models[role]

            self.logger.info(f"Generating response with {model_config.name} for role: {role}")

            start_time = time.time()

            response = self.ollama_client.generate(
                model=model_config.name,
                prompt=prompt,
                options={
                    'temperature': model_config.temperature,
                    'num_predict': model_config.max_tokens,
                }
            )

            duration = time.time() - start_time

            if not response or 'response' not in response:
                raise LLMResponseError("Invalid response format from Ollama")

            response_text = response['response'].strip()

            self.logger.info(f"Response generated in {duration:.2f}s, length: {len(response_text)} chars")

            return response_text

        except Exception as e:
            self.logger.error(f"Failed to generate response: {e}")
            if isinstance(e, (LLMConnectionError, LLMResponseError)):
                raise
            raise LLMResponseError(f"Unexpected error during generation: {e}")

    def generate_response_with_context(self, prompt: str, context: str = "", role: str = "assistant") -> str:
        """
        Generate response using specified model role with conversation context

        Args:
            prompt: Input prompt for the model
            context: Previous conversation context
            role: Model role (frontend, backend, debugger, assistant)

        Returns:
            Generated response text

        Raises:
            LLMConnectionError: If connection to Ollama fails
            LLMResponseError: If model response is invalid
        """
        # Build context-aware prompt
        if context.strip():
            full_prompt = f"""Previous conversation:
{context}

Current request: {prompt}

Please respond appropriately based on the conversation context."""
        else:
            full_prompt = prompt

        return self.generate_response(full_prompt, role)

    def get_model(self, role: str) -> Optional[ModelConfig]:
        """Get model configuration for specific role"""
        return self.models.get(role)
