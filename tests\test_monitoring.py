"""
Unit tests for monitoring and performance features
"""

import pytest
import sys
import os
import time
import tempfile
from unittest.mock import patch, MagicMock

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.monitoring import PerformanceMonitor, CircuitBreaker, get_performance_monitor
from src.database import DatabaseManager

class TestPerformanceMonitor:
    """Test cases for PerformanceMonitor"""
    
    def setup_method(self):
        """Set up test environment"""
        # Use temporary database for testing
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.database = DatabaseManager(self.temp_db.name)
        self.monitor = PerformanceMonitor(self.database)
    
    def teardown_method(self):
        """Clean up after each test"""
        # Close database connections first
        if hasattr(self, 'database'):
            del self.database
        if hasattr(self, 'monitor'):
            del self.monitor
        
        # Remove temporary database
        try:
            if os.path.exists(self.temp_db.name):
                os.unlink(self.temp_db.name)
        except PermissionError:
            # On Windows, sometimes the file is still locked
            pass
    
    def test_performance_monitor_initialization(self):
        """Test performance monitor initialization"""
        assert self.monitor is not None
        assert self.monitor.database is not None
        assert len(self.monitor.metrics) == 0
    
    def test_measure_operation_context_manager(self):
        """Test the measure_operation context manager"""
        operation_name = "test_operation"
        
        with self.monitor.measure_operation(operation_name):
            time.sleep(0.1)  # Simulate some work
        
        # Check that metric was recorded
        assert len(self.monitor.metrics) == 1
        metric = self.monitor.metrics[0]
        assert metric.operation == operation_name
        assert metric.duration_ms >= 100  # Should be at least 100ms
        assert metric.success is True
    
    def test_measure_operation_with_exception(self):
        """Test measure_operation when an exception occurs"""
        operation_name = "failing_operation"
        
        with pytest.raises(ValueError):
            with self.monitor.measure_operation(operation_name):
                raise ValueError("Test exception")
        
        # Check that metric was recorded with failure
        assert len(self.monitor.metrics) == 1
        metric = self.monitor.metrics[0]
        assert metric.operation == operation_name
        assert metric.success is False
        assert metric.error_message == "Test exception"
    
    def test_performance_summary(self):
        """Test performance summary generation"""
        # Record some test metrics
        with self.monitor.measure_operation("test_op_1"):
            time.sleep(0.05)
        
        with self.monitor.measure_operation("test_op_2"):
            time.sleep(0.03)
        
        with self.monitor.measure_operation("test_op_1"):
            time.sleep(0.02)
        
        # Get summary
        summary = self.monitor.get_performance_summary(hours=1)
        
        assert summary["total_operations"] == 3
        assert summary["successful_operations"] == 3
        assert summary["success_rate_percent"] == 100.0
        assert "test_op_1" in summary["operation_stats"]
        assert "test_op_2" in summary["operation_stats"]
        assert summary["operation_stats"]["test_op_1"]["count"] == 2
        assert summary["operation_stats"]["test_op_2"]["count"] == 1
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    @patch('psutil.disk_usage')
    def test_system_health_check(self, mock_disk, mock_memory, mock_cpu):
        """Test system health monitoring"""
        # Mock system metrics
        mock_cpu.return_value = 45.0
        mock_memory.return_value = MagicMock(percent=60.0)
        mock_disk.return_value = MagicMock(percent=70.0)
        
        health = self.monitor.get_system_health()
        
        assert health.cpu_usage_percent == 45.0
        assert health.memory_usage_percent == 60.0
        assert health.disk_usage_percent == 70.0
        assert health.overall_status == "healthy"
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    @patch('psutil.disk_usage')
    def test_system_health_warning_status(self, mock_disk, mock_memory, mock_cpu):
        """Test system health warning status"""
        # Mock high resource usage
        mock_cpu.return_value = 95.0
        mock_memory.return_value = MagicMock(percent=85.0)
        mock_disk.return_value = MagicMock(percent=95.0)
        
        health = self.monitor.get_system_health()
        
        assert health.overall_status == "warning"

class TestCircuitBreaker:
    """Test cases for CircuitBreaker"""
    
    def setup_method(self):
        """Set up test environment"""
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1,  # 1 second for testing
            expected_exception=Exception
        )
    
    def test_circuit_breaker_closed_state(self):
        """Test circuit breaker in closed state"""
        def successful_function():
            return "success"
        
        # Should work normally in closed state
        result = self.circuit_breaker.call(successful_function)
        assert result == "success"
        assert self.circuit_breaker.state == "closed"
    
    def test_circuit_breaker_opens_after_failures(self):
        """Test circuit breaker opens after threshold failures"""
        def failing_function():
            raise Exception("Test failure")
        
        # Cause failures to reach threshold
        for i in range(3):
            with pytest.raises(Exception):
                self.circuit_breaker.call(failing_function)
        
        # Circuit should now be open
        assert self.circuit_breaker.state == "open"
        
        # Next call should fail immediately without calling function
        with pytest.raises(Exception, match="Circuit breaker is open"):
            self.circuit_breaker.call(failing_function)
    
    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after timeout"""
        def failing_function():
            raise Exception("Test failure")
        
        def successful_function():
            return "success"
        
        # Open the circuit
        for i in range(3):
            with pytest.raises(Exception):
                self.circuit_breaker.call(failing_function)
        
        assert self.circuit_breaker.state == "open"
        
        # Wait for recovery timeout
        time.sleep(1.1)
        
        # Should attempt reset and succeed
        result = self.circuit_breaker.call(successful_function)
        assert result == "success"
        assert self.circuit_breaker.state == "closed"
    
    def test_circuit_breaker_decorator(self):
        """Test circuit breaker as decorator"""
        @self.circuit_breaker
        def decorated_function(should_fail=False):
            if should_fail:
                raise Exception("Decorated failure")
            return "decorated success"
        
        # Should work normally
        result = decorated_function(should_fail=False)
        assert result == "decorated success"
        
        # Should handle failures
        with pytest.raises(Exception):
            decorated_function(should_fail=True)

class TestMonitoringIntegration:
    """Integration tests for monitoring features"""
    
    def setup_method(self):
        """Set up test environment"""
        # Use temporary database for testing
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.database = DatabaseManager(self.temp_db.name)
    
    def teardown_method(self):
        """Clean up after each test"""
        # Close database connections first
        if hasattr(self, 'database'):
            del self.database
        
        # Remove temporary database
        try:
            if os.path.exists(self.temp_db.name):
                os.unlink(self.temp_db.name)
        except PermissionError:
            # On Windows, sometimes the file is still locked
            pass
    
    def test_performance_monitor_database_integration(self):
        """Test performance monitor integration with database"""
        monitor = PerformanceMonitor(self.database)
        
        # Record a metric
        with monitor.measure_operation("database_test", model_used="test_model", tokens_used=100):
            time.sleep(0.05)
        
        # Check that metric was saved to database
        stats = self.database.get_performance_stats()
        assert stats["count"] == 1
        assert stats["total_tokens"] == 100
    
    def test_global_performance_monitor(self):
        """Test global performance monitor instance"""
        monitor1 = get_performance_monitor(self.database)
        monitor2 = get_performance_monitor(self.database)
        
        # Should return the same instance
        assert monitor1 is monitor2
    
    @patch('requests.get')
    def test_ollama_health_check(self, mock_get):
        """Test Ollama health check"""
        monitor = PerformanceMonitor(self.database)
        
        # Mock successful Ollama response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        status = monitor._check_ollama_health()
        assert status == "healthy"
        
        # Mock failed Ollama response
        mock_response.status_code = 500
        status = monitor._check_ollama_health()
        assert status == "unhealthy"
        
        # Mock connection error
        mock_get.side_effect = Exception("Connection failed")
        status = monitor._check_ollama_health()
        assert status == "unavailable"
    
    def test_database_health_check(self):
        """Test database health check"""
        monitor = PerformanceMonitor(self.database)
        
        # Should be healthy with working database
        status = monitor._check_database_health()
        assert status == "healthy"
        
        # Test with no database
        monitor_no_db = PerformanceMonitor(None)
        status = monitor_no_db._check_database_health()
        assert status == "not_configured"

class TestPerformanceThresholds:
    """Test performance threshold monitoring"""
    
    def setup_method(self):
        """Set up test environment"""
        self.monitor = PerformanceMonitor()
        # Set low thresholds for testing
        self.monitor.thresholds = {
            'test_operation_time_ms': 50,  # 50ms threshold
            'memory_usage_percent': 80,
            'cpu_usage_percent': 90,
        }
    
    def test_threshold_warning(self):
        """Test that threshold warnings are logged"""
        # This would require checking log output in a real implementation
        # For now, we'll just ensure the method runs without error
        
        with self.monitor.measure_operation("test_operation"):
            time.sleep(0.06)  # Exceed 50ms threshold
        
        # Should have recorded the metric
        assert len(self.monitor.metrics) == 1
        metric = self.monitor.metrics[0]
        assert metric.duration_ms > 50
