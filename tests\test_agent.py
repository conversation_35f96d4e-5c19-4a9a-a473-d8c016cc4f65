# AI Coding Agent - Agent Tests
"""
Unit tests for the main CodingAgent class
"""

import pytest
import sys
import os

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.agent import CodingAgent


class TestCodingAgent:
    """Test cases for CodingAgent class"""
    
    def test_agent_initialization(self):
        """Test that agent initializes correctly"""
        agent = CodingAgent()
        
        # Check that session_id is generated
        assert agent.session_id is not None
        assert len(agent.session_id) > 0
        
        # Check that projects directory exists
        assert agent.projects_dir.exists()
        
    def test_generate_code_basic(self):
        """Test basic code generation functionality"""
        agent = CodingAgent()
        
        # Test with default language (python)
        result = agent.generate_code("create a hello world function")
        assert isinstance(result, str)
        assert "hello world" in result.lower()
        
        # Test with specified language
        result = agent.generate_code("create a hello world function", "javascript")
        assert isinstance(result, str)
        assert "javascript" in result.lower()
        
    def test_session_id_uniqueness(self):
        """Test that each agent instance has a unique session ID"""
        agent1 = CodingAgent()
        agent2 = CodingAgent()
        
        assert agent1.session_id != agent2.session_id
